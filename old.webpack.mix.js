const path = require('path');
const mix = require('laravel-mix');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const { BugsnagSourceMapUploaderPlugin } = require('webpack-bugsnag-plugins');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */
const TARGET = process.env.NODE_ENV;

mix.webpackConfig({
    devtool: TARGET === 'production' ? 'source-map' : 'eval-source-map', // Adjusted for Webpack 5
    entry: {
        app: './resources/js/app.js',
    },
    output: {
        filename: "[name].[chunkhash].js",
        chunkFilename: '[name].[chunkhash].js',
        path: path.resolve(__dirname, 'public/js/app'),
        publicPath: TARGET === 'production' ? 'https://www.shopeichlers.com/js/app/' : '/js/app/',
    },
    plugins: [
        new CleanWebpackPlugin(),
        ...(TARGET === 'production'
            ? [
                new BugsnagSourceMapUploaderPlugin({
                    apiKey: 'c7e442274488ecc6a1466576daea0be0',
                    appVersion: '1.1.2',
                    overwrite: true,
                }),
            ]
            : []),
    ],
    module: {
        rules: [
            {
                test: /\.vue$/,
                loader: 'vue-loader', // Add vue-loader for .vue files
            },
            {
                test: /node_modules\/(vuex-persist|vuejs-clipper)\/.+\.js$/,
                use: [
                    {
                        loader: 'babel-loader',
                        options: {
                            presets: [
                                [
                                    '@babel/preset-env',
                                    {
                                        targets: '> 0.25%, not dead',
                                    },
                                ],
                            ],
                        },
                    },
                ],
            },
        ],
    },
});

mix.js('resources/js/app.js', 'public/js')
    .vue() // Add this line to enable Vue support in Mix
    .sass('resources/sass/app.scss', 'public/css')
    .sourceMaps();
