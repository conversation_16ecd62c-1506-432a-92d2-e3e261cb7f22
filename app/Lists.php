<?php

namespace App;

use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Lists extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    protected $table = 'lists';

    public function school()
    {
        return $this->belongsTo(School::class);
    }

    public function bundle()
    {
        return $this->hasOne(Bundle::class);
    }

    public function setFormattedProductsAttribute($products)
    {
        return;
    }

    public function getFormattedProductsAttribute()
    {
        if (!$bundle = $this->bundle) {
            return [];
        }
        return $bundle->bundleItems->map(function ($item) {
            if (!$model = $item->model) {
                return;
            }
            $data = $model->getFrontEndAttribute();
            $return = [
                'id' => $model->id,
                'type' => $data['type'],
                'title' => $model->title,
                'price' => $model->toArray()['price'] * $item->quantity,
                'media' => $model->getFirstMediaUrl('media'),
                'sku' => $model->sku,
                'quantity' => $item->quantity,
                'max' => $model->max,
                'vendor' => $data['vendor'],
            ];
            if ($data['type'] == 'variation') {
                $return['meta'] = json_decode($model->meta);
                $return['title'] = $model->product->title;
                $return['media'] = $model->product->getFirstMediaUrl('media');
            }
            return $return;
        })->filter();
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);
        $this->addMediaConversion('thumbnail')
            ->width(100);
        $this->addMediaConversion('grid')
            ->width(250);
        $this->addMediaConversion('large')
            ->width(500);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('media');
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($list) {
            $bundle = Bundle::firstOrCreate(['lists_id' => $list->id]);
            $bundle->update([
                'title' => $list->grade,
                'price' => $list->price,
            ]);
            $ids = collect(json_decode(request()->formatted_products))->map(function ($item) use ($list, $bundle) {
                switch ($item->type) {
                    case 'product':
                        $type = 'App\Product';
                        break;

                    case 'variation':
                        $type = 'App\VariationInfo';
                        break;
                }
                $prod = $bundle->bundleItems()->firstOrCreate([
                    'model_type' => $type,
                    'model_id' => $item->id,
                ]);

                $prod->update([
                    'quantity' => $item->quantity
                ]);

                return $prod->id;
            });

            $bundle->bundleItems()->whereNotIn('id', $ids)->delete();
        });
    }
}
