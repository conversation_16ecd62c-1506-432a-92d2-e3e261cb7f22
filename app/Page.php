<?php

namespace App;

use Cache;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Page extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    protected $casts = [
        'components' => 'array',
    ];

    protected $appends = [
        'path',
        'media_urls',
    ];

    public static $options = [
        'track' => 'Track',
        'banner' => 'Banner',
    ];

    public function getPathAttribute()
    {
        return "/" . str_slug($this->title) . "/{$this->id}";
    }

    public function getMediaUrlsAttribute()
    {
        return $this->media->sortBy('order_column')->map(function ($item) {
            return $item->getUrl();
        });
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);

        $this->addMediaConversion('1800')
            ->width(1800);

        $this->addMediaConversion('1430')
            ->width(1430);

        $this->addMediaConversion('780')
            ->width(780);

        $this->addMediaConversion('470')
            ->width(470);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('images');

        $this->addMediaCollection('mobile');

        $this->addMediaCollection('media')
            ->singleFile();
    }

    public static function refreshCache($page)
    {
        Cache::tags(['pages'])->forget('page_' . $page->id);
        Cache::tags('redirects')->flush();
    }

    protected static function boot()
    {
        parent::boot();
        static::saved(function ($page) {
            if ($page->name = 'Home') {
                Cache::tags(['pages'])->forget('pages');
            }
            self::refreshCache($page);
        });
        static::deleted(function ($page) {
            self::refreshCache($page);
        });
    }
}
