<?php

namespace App;

use App\Jobs\Miscellaneous;
use <PERSON>lnoy\Nestedset\NodeTrait;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\MediaLibrary\HasMedia;
use <PERSON>tie\MediaLibrary\InteractsWithMedia;

class Category extends Model implements HasMedia
{
    use NodeTrait, InteractsWithMedia;

    protected $casts = [
        'start' => 'date',
        'end' => 'date',
    ];

    protected $appends = ['path'];
    // protected $with = ['filters'];

    protected $guarded = [];

    public function products()
    {
        return $this->belongsToMany(Product::class);
    }

    public function filters()
    {
        return $this->belongsToMany(Filter::class);
    }

    public function getPathAttribute()
    {
        return "/categories/" . str_slug($this->name) . "/{$this->id}";
    }

    public function scopeActive($query)
    {
        $now = now();

        $query->where(function ($query) use ($now) {
            $query->where('start', null)->orWhere('start', '<', $now);
        });

        $query->where(function ($query) use ($now) {
            $query->where('end', null)->orWhere('end', '>', $now);
        });

        return $query;
    }

    public function getMediaUrlsAttribute()
    {
        return $this->media->sortBy('order_column')->map(function ($item) {
            return $item->getUrl();
        });
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);

        $this->addMediaConversion('thumbnail')
            ->width(84)
            ->height(84);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('media')
            ->singleFile();
    }

    public function refreshCache($category)
    {
        $category->products->each->refreshCache();
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($category) {
            if ($category->isDirty('name')) {
                $category->products->pluck('id')->chunk(50)->map(function ($ids) {
                    dispatch(function () use ($ids) {
                        Product::find($ids)->map->updateSearchFeild();
                    });
                });
                Miscellaneous::dispatch(Category::class, 'refreshCache', $category);
            }
            refreshCache();
        });

        static::deleted(function ($category) {
            refreshCache();
        });
    }
}
