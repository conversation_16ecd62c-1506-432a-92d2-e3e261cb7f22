<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Str;

class RobotsMiddleware
{
    protected $response;

    public function handle($request, Closure $next)
    {
        $this->response = $next($request);

        if (!Str::endsWith(request()->getHttpHost(), 'shopeichlers.com')) {
            $this->response->headers->set('x-robots-tag', 'none', false);
        }

        return $this->response;
    }
}
