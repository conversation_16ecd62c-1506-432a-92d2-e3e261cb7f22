<?php

use App\Bundle;
use App\GiftCard;
use App\Product;
use App\VariationInfo;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

function tail($info)
{
    Log::channel('tail')->info($info);
}

function forceArray($array)
{
    return (is_array($array) ? $array : json_decode($array, true)) ?: [];
}

function forceObject($object)
{
    return is_object($object) ? $object : json_decode(json_encode($object));
}

function array_combinations(array $data, array &$all = array(), array $group = array(), $value = null, $i = 0)
{
    $keys = array_keys($data);
    if (isset($value) === true) {
        array_push($group, $value);
    }

    if ($i >= count($data)) {
        array_push($all, $group);
    } else {
        $currentKey = $keys[$i];
        $currentElement = $data[$currentKey];
        foreach ($currentElement as $val) {
            array_combinations($data, $all, $group, $val, $i + 1);
        }
    }

    return $all;
}

function decode($encrypted)
{
    try {
        return decrypt($encrypted);
    } catch (Illuminate\Contracts\Encryption\DecryptException $e) {
        return $encrypted;
    }
}

function makeForFronEnd($bags)
{
    return $bags->map(function ($bag) {
        return $bag->getFrontEndAttribute($bag->quantity);
    })->filter()->values();
}

function GetFromFrontEnd($bags)
{
    return collect($bags)->map(function ($item) {
        $item = forceObject($item);

        if ($item->type == 'giftCard') {
            $type = 'App\GiftCard';
            $meta = $item;
            $item->id = null;
        } else {
            if ($item->type == 'product') {
                $type = 'App\Product';
            } elseif ($item->type == 'variation') {
                $type = 'App\VariationInfo';
            } elseif ($item->type == 'bundle') {
                $type = 'App\Bundle';
            }
            $meta['gift_options'] = property_exists($item, 'gift_options') ? $item->gift_options : null;
            $meta['discount_amount'] = property_exists($item, 'discount_amount') ? $item->discount_amount : null;
            $meta['total_amount'] = property_exists($item, 'total_amount') ? $item->total_amount : null;
            $meta['tax_amount'] = property_exists($item, 'tax_amount') ? $item->tax_amount : null;
            $meta['personalization'] = property_exists($item, 'personalization') ? $item->personalization : null;
            $meta['is_personalization'] = property_exists($item, 'is_personalization') ? $item->is_personalization : false;
            $meta['personal'] = property_exists($item, 'personal') ? $item->personal : null;
        }

        $bag = App\Bag::make([
            'model_id' => $item->id,
            'model_type' => $type,
            'quantity' => $item->quantity,
            'meta' => $meta ?? null,
            'item_key' => data_get($item, 'item_key')
        ]);
        if ($bag->model_type == 'App\GiftCard' || optional($bag->model)->exists()) {
            return $bag;
        }
    })
        ->filter()
        ->values();
}

function settings()
{
    return new App\Setting;
}

function getModel($type, $id)
{
    switch ($type) {
        case 'product':
            return Product::findOrFail($id);
            break;
        case 'variation':
            return VariationInfo::findOrFail($id);
            break;
        case 'bundle':
            return Bundle::findOrFail($id);
            break;
        case 'giftCard':
            return GiftCard::findOrFail($id);
            break;
    }
}

function customer()
{
    return auth()->guard('api')->user() ?? abort(403);
}

function refreshCache()
{
    Cache::remember('refresh', 60 * 5, function () {
        return true;
    });
}

function l($value)
{
    Log::info($value);
}

function idsToSwiftype($ids, $delete = false)
{
    $key = 'swiftype_' . (!$delete ? 'update' : 'delete') . '_ids';
    settings()->setValue(
        $key,
        collect(json_decode(settings()->getValue($key)))
            ->merge($ids)
            ->unique()
            ->values()
    );
}

if (!function_exists('starts_with')) {
    function starts_with($a, $b)
    {
        return Str::startsWith($a, $b);
    }
}
if (!function_exists('ends_with')) {
    function ends_with($a, $b)
    {
        return Str::endsWith($a, $b);
    }
}
if (!function_exists('str_slug')) {
    function str_slug($slug)
    {
        return Str::slug($slug);
    }
}
if (!function_exists('array_where')) {
    function array_where($obj, $func)
    {
        return Arr::where($obj, $func);
    }
}
if (!function_exists('array_first')) {
    function array_first($array)
    {
        return Arr::first($array);
    }
}
if (!function_exists('array_flatten')) {
    function array_flatten($array, $depth = INF)
    {
        return Arr::flatten($array, $depth = INF);
    }
}
