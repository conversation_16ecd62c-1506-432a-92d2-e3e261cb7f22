<?php

namespace App\Http\Controllers\Api;

use App\Customer;
use App\Discount;
use App\GiftCard;
use App\Http\Controllers\Controller;
use App\Order;
use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Jenssegers\Agent\Agent;
use Throwable;

class OrdersController extends Controller
{
    public $customer;
    public $bag;

    public function __construct()
    {
        $this->middleware('csrf')->only(['create', 'csrf']);
    }

    public function csrf()
    {
        return response(200);
    }

    public function index()
    {
        return customer()
            ->orders()->with(['returns'])->notDeclined()->where('guest', false)->get()->map(function ($order) {
                //digital
                $products = collect($order->products)->map(function ($item) {
                    if (
                        isset($item['item_type'])
                        && ($item['item_type'] == 'digital' || $item['item_type'] == 'both')
                    ) {
                        return $item + [
                                'download' => '/api' . $item['path'] . '/download',
                            ];
                    } else {
                        return $item;
                    }
                });
                return collect($order)->merge(['products' => $this->returnProducts($order)]);
            });
    }

    public function returnProducts($order)
    {
        $returns = $order->returns;
        if ($returns->count() > 0) {
            $return_products = $returns
                ->map
                ->products
                ->flatten(1)
                ->map(function ($product) {
                    $product['temp_id'] = data_get($product, 'id') . '-' . data_get($product, 'item_key');
                    return $product;
                })
                ->groupBy(['temp_id', 'type'])
                ->map->map(function ($item) {
                    $sum = $item->pluck('quantity')->sum();
                    return ['quantity' => $sum] + $item[0];
                })
                ->flatten(1);

            return collect($order->products)->map(function ($product) use ($return_products, $returns, $order) {
                $exists = $return_products->filter(function ($item) use ($product) {
                    return $item['type'] == $product['type'] && $item['id'] == $product['id'] && data_get(
                            $product,
                            'item_key'
                        ) == data_get($item, 'item_key');
                });

                if ($exists->count() > 0) {
                    $product['returned'] = [
                        'status' => data_get($exists->last(), 'status'),
                    ];
                    if ($exists->pluck('quantity')->sum() != $product['quantity']) {
                        $product['returned']['string'] = $exists->pluck('quantity')->sum(
                            ) . ' of ' . $product['quantity'];
                    } else {
                        $product['returned']['string'] = $product['quantity'];
                    }
                    $label = $returns->filter(function ($return) use ($product) {
                        return collect($return->products)->filter(function ($item) use ($product) {
                            return $item['id'] == $product['id'] && $item['type'] == $product['type'] && data_get(
                                    $product,
                                    'item_key'
                                ) == data_get($item, 'item_key');
                        });
                    })->last();
                    if ($label) {
                        $product['returned']['label'] = data_get($label, 'shipping.tracking.label_pdf');
                    }
                }
                $returns_left = $product['quantity'] - $exists->pluck('quantity')->sum();
                if ($returns_left > 0 && $product['item_type'] != 'digital' && $product['item_type'] != 'service') {
                    $product['returns_left'] = $product['quantity'] - $exists->pluck('quantity')->sum();
                } else {
                    $product['returns_left'] = 0;
                }
                if ($product['item_type'] == 'digital' || $product['item_type'] == 'both') {
                    $product['download'] = '/api' . $product['path'] . '/download?token=' . encrypt($order->id);
                }
                return $product;
            })->filter()->values();
        } else {
            return collect($order->products)->map(function ($product) use ($order) {
                if ($product['item_type'] != 'digital' && $product['item_type'] != 'service' && !data_get(
                        $product,
                        'exclude_from_returns'
                    )) {
                    $product['returns_left'] = $product['quantity'];
                } else {
                    $product['returns_left'] = 0;
                }
                if ($product['item_type'] == 'digital' || $product['item_type'] == 'both') {
                    $product['download'] = '/api' . $product['path'] . '/download?token=' . encrypt($order->id);
                }
                return $product;
            })->filter()->values();
        }
    }

    public function show($id)
    {
        if (auth()->guard('admin')->check()) {
            //you wont be able to download
            return Order::find(decode($id));
        }
        try {
            $decrypted = decrypt($id);
            $order = Order::find($decrypted) ?? abort(404);
        } catch (DecryptException $e) {
            $user = auth()->guard('api')->user();
            $user ? $order = $user->orders()->findOrFail($id) : abort(404);
        }
        $order->products = collect($order->products)->map(function ($item) use ($order) {
            if (($item['item_type'] == 'digital' || $item['item_type'] == 'both')) {
                return [
                        'download' => '/api' . $item['path']
                            . '/download?token='
                            . encrypt($order->id),
                    ] + $item;
            } else {
                return $item;
            }
        });
        return collect($order)->merge(['products' => $this->returnProducts($order)->values()]);
    }

    public function create(Request $request)
    {
        Log::channel('orders')->info($request->all());

        if (!request('recaptcha_token')) {
            l('HACKER');
            abort(403);
        }

        $request->recaptcha = $this->recaptcha();

        $request->sessionId = session()->getId();

        $request->bag = ProductsController::getMax(request()->merge(['products' => $request->bag]));

        $request->bag = collect($request->bag)->filter(function ($b) {
            return $b['quantity'] > 0 && data_get($b, 'max') > 0;
        });

        if (collect($request->bag)->isEmpty()) {
            return;
        }

        $this->customer = Customer::getCustomer($request->customer);

        $this->giftCards = GiftCard::getCards($request->giftCards);

        $this->getDiscount($request);

        $order = $this->placeOrder($request);

        $this->customer->bag->where('later', false)->each->delete();

        return [
            'order_id' => $order->id,
            'id' => auth()->guard('api')->check() ? $order->id : encrypt($order->id),
            'user' => data_get($request->customer, 'loginInfo') ? $this->customer : null,
        ];
    }

    public function recaptcha()
    {
        return 'true_0.9';
        $url = 'https://www.google.com/recaptcha/api/siteverify';
        $data = [
            'secret' => env('GOOGLE_RECAPTCHA_SECRET'),
            'response' => request('recaptcha_token'),
        ];

        $options = [
            'http' => [
                'header' => 'Content-type: application/x-www-form-urlencoded\r\n',
                'method' => 'POST',
                'content' => http_build_query($data)
            ]
        ];
        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);
        $resultJson = json_decode($result);

        return json_encode(data_get($resultJson, 'success')) . '_' . data_get($resultJson, 'score');

        if ($resultJson->success != true) {
            return back()->with('message', 'Captcha Error');
        }
    }

    public function getDiscount($request)
    {
        $this->discount = Discount::find($request->discountId);

        if (!$this->discount) {
            return;
        }

        $zip = data_get($request->customer, 'shippingInfo')
            ?? data_get($request->customer, 'creditInfo');

        $min = (new DiscountController)->fullPrice(getFromFrontEnd($request->bag));

        if (!optional($this->discount)->confirmValidity($min, $zip)) {
            $this->discount = null;
        }

        // if ($this->discount->type == 'freeShipping') {
        //     $shippingSavings = (new shippingController)
        //         ->price(
        //             data_get($request, 'customer.shippingType.id'),
        //             getFromFrontEnd($request->bag),
        //             $zip
        //         );
        //     if ($shippingSavings == 0) {
        //         $this->discount = null;
        //     }
        // }
    }

    public function placeOrder($request)
    {
        $shippingInfo = !empty(data_get($request->customer, 'shippingInfo'))
            ? data_get($request->customer, 'shippingInfo')
            : data_get($request->customer, 'creditInfo');

        unset($shippingInfo['password']);

        $savingsData = optional($this->discount)
            ->getSavings(
                GetFromFrontEnd($request->bag),
                collect($shippingInfo)
            ) ?? null;

        $totals = (new BagController)->GetBreakDown(request()->merge([
            'products' => $request->bag,
            'address' => $shippingInfo,
            'discount_id' => $request->discountId,
            'shippingId' => data_get($request, 'customer.shippingType.id')
        ]));

        $shippingSavings = 0;
        if ($this->discount && $this->discount->type == 'freeShipping') {
            $shippingSavings = $totals['original_shipping'];
        }

        if ($shippingSavings) {
            $savingsData['savings'] = $shippingSavings;
        }

        $charge = $totals['total'];
        $giftCardInfo = $this->giftCards->map(function ($card) use (&$charge) {
            $amount = $card->balance < $charge ? $card->balance : $charge;
            $charge = $charge - $amount;
            return [
                'id' => $card->id,
                'amount' => $amount,
                'code' => substr($card->code, -4),
            ];
        });

        $agent = new Agent();
        $device = $agent->isDesktop() ? 'Desktop' : null;
        $device = $device ? $device : ($agent->isMobile() ? 'Mobile' : null);
        $device = $device ? $device : ($agent->isTablet() ? 'Tablet' : null);

        return $this->customer->orders()->create([
            'sub_total' => $totals['sub_total'],
            'tax_amount' => $totals['tax'],
            'shipping_amount' => $totals['shipping'],
            'meta->gift_options' => $totals['giftOptions'],
            'discount' => $savingsData,
            'discount_amount' => data_get($savingsData, 'savings'),
            'discount_id' => data_get($this, 'discount.id'),
            'grand_total' => $totals['total'],
            'products' => $totals['bag'],
            'shipping' => [
                'shippingInfo' => $shippingInfo,
                'shippingType' => $request->customer['shippingType'],
                'tracking' => null,
                'pickupInfo' => data_get($request->customer, 'pickupInfo')
            ],
            'payments' => [
                'creditInfo' => data_get($request->customer, 'creditInfo'),
                'giftCard' => $giftCardInfo,
            ],
            'guest' => auth()->guard('api')->guest(),
            'payment_status' => 'unpaid',
            'status' => 'unpaid',
            'product_ids' => $totals['bag']->pluck('product_id')->filter(),
            'meta->re' => $request->recaptcha,
            'meta->shipping_tax' => $totals['tax'] - $totals['bag']->sum('tax_amount'),
            'meta->request' => [
                'ip' => $request->ip(),
                'platform' => $agent->platform(),
                'browser' => $agent->browser(),
                'plaform_version' => $agent->version($agent->platform()),
                'system' => $agent->device(),
                'device' => $device,
                'utm' => Cache::pull($request->sessionId)
            ]
        ]);
    }

    public function lookup(Request $request)
    {
        if ($request->sku) {
            return $this->skuLookup($request);
        }
        try {
            $order = Customer::where([
                'email' => $request->email,
            ])->first()->orders()->findOrFail($request->order_id);

            if ($order->return_by->isBefore(today())) {
                return response()->json([
                    'message' => 'The given data was invalid.',
                    'errors' => ['order' => 'Sorry, this order is no longer available for return.']
                ], 422);
            }

            return [
                'return_by' => $order->created_at->addDays(30),
                'products' => $this->returnProducts($order),
            ];
        } catch (Throwable $th) {
            abort(404);
        }
    }

    public function skuLookup($request)
    {
        try {
            $order = Order::findOrFail($request->order_id);
            if ($order->return_by->isBefore(today())) {
                return response()->json([
                    'message' => 'The given data was invalid.',
                    'errors' => ['order' => 'Sorry, this order is no longer available for return.']
                ], 422);
            }
            $products = collect($this->returnProducts($order))->where('sku', $request->sku)->values();
            if (!$products->count()) {
                abort(404);
            }
            return [
                'return_by' => $order->created_at->addDays(30),
                'products' => $products,
            ];
        } catch (Throwable $th) {
            abort(404);
        }
    }
}
