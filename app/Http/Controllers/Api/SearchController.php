<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Product;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SearchController extends Controller
{
    private $forced;

    public function index(Request $request, $forced = null)
    {
        $this->forced = collect($forced);

        $query = $request->getRequestUri();
        return Cache::tags(['search'])->remember(
            "search_{$query}",
            60 * 60 * 24,
            function () use ($request, $forced) {
                $request->q = Str::limit($request->q, 100);
                $searchORFilters = [];
                $searchANDFilters = [];
                $numericFilters = [];
                if ($request->input('max') || $request->input('min')) {
                    $numericFilters[] = 'price:' . $request->input('min', 0) . ' TO ' . $request->input('max', 1000000);
                }

                $categoryFilter = [];
                if ($this->forced) {
                    $categories_all = $this->forced->get('all_categories');
                    $categories_and = $this->forced->get('and_categories');
                    $categories_or = $this->forced->get('any_categories');
                    $categories_not = $this->forced->get('none_categories');

                    if (!empty($categories_all)) {
                        $categories_all = collect(explode(',', $categories_all))->map(function ($category) {
                            return '"' . htmlspecialchars(urldecode($category)) . '"';
                        });
                        $categoryFilter[] = '(categories:' . implode(' AND categories:', $categories_all->toArray()) . ')';
                    }
                    if (!empty($categories_and) && sizeof($categories_and) > 0) {
                        $categories_and = collect($categories_and)->map(function ($category) {
                            return '"' . htmlspecialchars($category) . '"';
                        });
                        $categoryFilter[] = '(categories:' . implode(' AND categories:', $categories_and->toArray()) . ')';
                    }
                    if (!empty($categories_or) && sizeof($categories_or) > 0) {
                        $categories_or = collect($categories_or)->map(function ($category) {
                            return '"' . htmlspecialchars($category) . '"';
                        });
                        $searchORFilters[] = '(categories:' . implode(' OR categories:', $categories_or->toArray()) . ')';
                    }
                    if (!empty($categories_not) && sizeof($categories_not) > 0) {
                        $categories_not = collect($categories_not)->map(function ($category) {
                            return '"' . htmlspecialchars($category) . '"';
                        });
                        $categoryFilter[] = '(NOT categories:' . implode(' AND NOT categories:', $categories_not->toArray()) . ')';
                    }
                }

                if ($request->input('categories')) {
                    $categories = collect(explode(',', $request->input('categories')));
                    if(!empty($categories)){
                        $categories = $categories->map(function ($category) {
                            return '"' . htmlspecialchars(urldecode($category)) . '"';
                        });
                        $categoryFilter[] = '(categories:' . implode(' AND categories:', $categories->toArray()) . ')';
                    }
                }
                if (sizeof($categoryFilter) > 0){
                    $searchANDFilters[] = implode(' AND ', $categoryFilter);
                }

                $tagFilter = [];
                if ($this->forced) {
                    $tags_and = $this->forced->get('and_tags');
                    $tags_or = $this->forced->get('any_tags');
                    $tags_not = $this->forced->get('none_tags');
                    if (!empty($tags_and) && sizeof($tags_and) > 0) {
                        $tags_and = collect($tags_and)->map(function ($tag) {
                            return '"' . htmlspecialchars($tag) . '"';
                        });
                        $tagFilter[] = '(tags:' . implode(' AND tags:', $tags_and->toArray()) . ')';
                    }
                    if (!empty($tags_or) && sizeof($tags_or) > 0) {
                        $tags_or = collect($tags_or)->map(function ($tag) {
                            return '"' . htmlspecialchars($tag) . '"';
                        });
                        $searchORFilters[] = '(tags:' . implode(' OR tags:', $tags_or->toArray()) . ')';
                    }
                    if (!empty($tags_not) && sizeof($tags_not) > 0) {
                        $tags_not = collect($tags_not)->map(function ($tag) {
                            return '"' . htmlspecialchars($tag) . '"';
                        });
                        $tagFilter[] = '(NOT tags:' . implode(' AND NOT tags:', $tags_not->toArray()) . ')';
                    }
                }
                if ($request->input('tags')) {
                    $tags = collect(explode(',', $request->input('tags')));
                    if (!empty($tags)) {
                        $tags = $tags->map(function ($tag) {
                            return '"' . htmlspecialchars(urldecode($tag)) . '"';
                        });
                        $tagFilter[] = '(tags:' . implode(' AND tags:', $tags->toArray()) . ')';
                    }
                }
                if (sizeof($tagFilter) > 0) {
                    $searchANDFilters[] = implode(' AND ', $tagFilter);
                }

                $creatorFilter = [];
                if ($this->forced) {
                    $creators_and = $this->forced->get('and_creators');
                    $creators_or = $this->forced->get('any_creator');
                    $creators_not = $this->forced->get('none_creator');
                    if (!empty($creators_and) && sizeof($creators_and) > 0) {
                        $creators_and = collect($creators_and)->map(function ($creator) {
                            return '"' . htmlspecialchars($creator) . '"';
                        });
                        $creatorFilter[] = '(creators:' . implode(' AND creators:', $creators_and->toArray()) . ')';
                    }
                    if (!empty($creators_or) && sizeof($creators_or) > 0) {
                        $creators_or = collect($creators_or)->map(function ($creator) {
                            return '"' . htmlspecialchars($creator) . '"';
                        });
                        $searchORFilters[] = '(creators:' . implode(' OR creators:', $creators_or->toArray()) . ')';
                    }
                    if (!empty($creators_not) && sizeof($creators_not) > 0) {
                        $creators_not = collect($creators_not)->map(function ($creator) {
                            return '"' . htmlspecialchars($creator) . '"';
                        });
                        $creatorFilter[] = '(NOT creators:' . implode(' AND NOT creators:', $creators_not->toArray()) . ')';
                    }
                }
                if ($request->input('creators')) {
                    $creators = collect(explode(',', $request->input('creators')));
                    if (!empty($creators)) {
                        $creators = $creators->map(function ($creator) {
                            return '"' .htmlspecialchars(urldecode($creator)) . '"';
                        });
                        $creatorFilter[] = '(creators:' . implode(' AND creators:', $creators->toArray()) . ')';
                    }
                }
                if (sizeof($creatorFilter) > 0) {
                    $searchANDFilters[] = implode(' AND ', $creatorFilter);
                }

                $vendorFilter = [];
                if ($this->forced) {
                    $vendors_and = $this->forced->get('and_vendors');
                    $vendors_or = $this->forced->get('any_vendors');
                    $vendors_not = $this->forced->get('none_vendors');
                    if (!empty($vendors_and) && sizeof($vendors_and) > 0) {
                        $vendors_and = collect($vendors_and)->map(function ($vendor) {
                            return '"' . htmlspecialchars($vendor) . '"';
                        });
                        $vendorFilter[] = '(vendor:' . implode(' AND vendor:', $vendors_and->toArray()) . ')';
                    }
                    if (!empty($vendors_or) && sizeof($vendors_or) > 0) {
                        $vendors_or = collect($vendors_or)->map(function ($vendor) {
                            return '"' . htmlspecialchars($vendor) . '"';
                        });
                        $searchORFilters[] = '(vendor:' . implode(' OR vendor:', $vendors_or->toArray()) . ')';
                    }
                    if (!empty($vendors_not) && sizeof($vendors_not) > 0) {
                        $vendors_not = collect($vendors_not)->map(function ($vendor) {
                            return '"' . htmlspecialchars($vendor) . '"';
                        });
                        $vendorFilter[] = '(NOT vendor:' . implode(' AND NOT vendor:', $vendors_not->toArray()) . ')';
                    }
                }
                if ($request->input('vendor')) {
                    $vendors = collect(explode(',', $request->input('vendor')));
                    if (!empty($vendors)) {
                        $vendors = $vendors->map(function ($vendor) {
                            return '"' . htmlspecialchars(urldecode($vendor)) . '"';
                        });
                        $vendorFilter[] = '(vendor:' . implode(' AND vendor:', $vendors->toArray()) . ')';
                    }
                }
                if (sizeof($vendorFilter) > 0) {
                    $searchANDFilters[] = implode(' AND ', $vendorFilter);
                }

                $filterFilter = [];

                if ($this->forced) {
                    $filters_and = $this->forced->get('and_filters');
                    $filters_or = $this->forced->get('any_filters');
                    $filters_not = $this->forced->get('none_filters');
                    if (!empty($filters_and) && sizeof($filters_and) > 0) {
                        $filters_and = collect($filters_and)->map(function ($filter) {
                            return '"' . htmlspecialchars($filter) . '"';
                        });
                        $filters_and = $filters_and->groupBy(function ($filter) {
                            return explode('_', $filter)[0];
                        });
                        $filters_and = $filters_and->map(function ($filter) {
                            return '(filters:' . implode(' AND filters:', $filter->toArray()) . ')';
                        });
                        $filterFilter[] = implode(' AND ', $filters_and->toArray());
                    }
                    if (!empty($filters_or) && sizeof($filters_or) > 0) {
                        $filters_or = collect($filters_or)->map(function ($filter) {
                            return '"' . htmlspecialchars($filter) . '"';
                        });
                        $filters_or = $filters_or->groupBy(function ($filter) {
                            return explode('_', $filter)[0];
                        });
                        $filters_or = $filters_or->map(function ($filter) {
                            return '(filters:' . implode(' OR filters:', $filter->toArray()) . ')';
                        });
                        $searchORFilters[] = implode(' OR ', $filters_or->toArray());
                    }
                    if (!empty($filters_not) && sizeof($filters_not) > 0) {
                        $filters_not = collect($filters_not)->map(function ($filter) {
                            return '"' . htmlspecialchars($filter) . '"';
                        });
                        $filters_not = $filters_not->groupBy(function ($filter) {
                            return explode('_', $filter)[0];
                        });
                        $filters_not = $filters_not->map(function ($filter) {
                            return '(NOT filters:' . implode(' AND NOT filters:', $filter->toArray()) . ')';
                        });
                        $filterFilter[] = implode(' AND ', $filters_not->toArray());
                    }
                }
                if ($request->input('filters')) {
                    $filters = explode(',', $request->input('filters'));
                    if (!empty($filters)) {
                        $filters = collect($filters)->map(function ($filter) {
                            return '"' . htmlspecialchars(urldecode($filter)) . '"';
                        });
                        $filters = $filters->groupBy(function ($filter) {
                            return explode('_', $filter)[0];
                        });
                        $filters = $filters->map(function ($filter) {
                            return '(filters:' . implode(' AND filters:', $filter->toArray()) . ')';
                        });
                        $filterFilter[] = implode(' AND ', $filters->toArray());
                    }
                }
                if (sizeof($filterFilter) > 0) {
                    $searchANDFilters[] = implode(' AND ', $filterFilter);
                }

                if ($request->input('questions')) {
                    $questions = explode(',', $request->input('questions'));
                    $questions = collect($questions)->map(function ($question) {
                        return '"' . htmlspecialchars(urldecode($question)) . '"';
                    });
                    $searchANDFilters[] = '(questions:' . implode(' AND questions:', $questions->toArray()) . ')';
                }

                if ($request->input('variations')) {
                    $variations = explode(',', $request->input('variations'));
                    $variations = collect($variations)->map(function ($variation) {
                        return '"' . htmlspecialchars(urldecode($variation)) . '"';
                    });
                    $searchANDFilters[] = '(variations:' . implode(' AND variations:', $variations->toArray()) . ')';
                }

                if($request->input('new')){
                    $searchANDFilters[] = '(release_date >= ' . now()->subDays(30 * 5)->timestamp . ')';
                }

                if(sizeof($searchORFilters) > 0){
                    $searchANDFilters[] = implode(' OR ', $searchORFilters);
                }

                switch ($request->sort) {
                    case 'release_date-desc':
                        $searchIndex = 'products_release_date_desc';
                        break;
                    case 'price-desc':
                        $searchIndex = 'products_price_desc';
                        break;
                    case 'price-asc':
                        $searchIndex = 'products_price_asc';
                        break;
                    default:
                        $searchIndex = 'products';
                        break;
                }

                $search = Product::search($request->q)
                    ->within($searchIndex)
                    ->with([
                        'facets' => ['categories', 'tags', 'creators', 'vendor', 'filters', 'questions', 'variations'],
                        'filters' => implode(' AND ', $searchANDFilters),
                        'numericFilters' => $numericFilters
                    ]);

                $rawData = $search->raw();

                $facets = collect(Arr::get($rawData, 'facets', []))
                    ->map(function ($facetValues, $facetName) {
                        return collect($facetValues)->map(function ($count, $name) {
                            return [
                                'count' => $count,
                                'name' => $name,
                            ];
                        })->values();
                    });

                if (isset($facets['filters']) && sizeof($facets['filters']) > 0) {
                    $facets['filters'] = $facets['filters']->groupBy(function ($item) {
                        return explode('_', $item['name'])[0];
                    })->map(function ($item) {
                        return $item->map(function ($item) {
                            $item['name'] = explode('_', $item['name'])[1];
                            $item['show'] = true;
                            return $item;
                        });
                    });
                }

                if (isset($facets['questions']) && sizeof($facets['questions']) > 0) {
                    $facets['questions'] = $facets['questions']->groupBy(function ($item) {
                        return explode('_', $item['name'])[0];
                    })->map(function ($item) {
                        return $item->map(function ($item) {
                            $item['name'] = explode('_', $item['name'])[1];
                            return $item;
                        });
                    });
                }

                if (isset($facets['variations']) && sizeof($facets['variations']) > 0) {
                    $facets['variations'] = $facets['variations']->groupBy(function ($item) {
                        return explode('_', $item['name'])[0];
                    })->map(function ($item) {
                        return $item->map(function ($item) {
                            $item['name'] = explode('_', $item['name'])[1];
                            return $item;
                        });
                    });
                }

                if (isset($facets['vendor']) && sizeof($facets['vendor']) > 0) {
                    $facets['vendors'] = $facets['vendor'];
                }

                $products = $search->paginate(40)->through(function ($product) {
                    $search = $product->search;
                    if (data_get($search, 'label')) {
                        $search['label'] = data_get($search, 'label');
                    } elseif ($date = data_get($search, 'release_date')) {
                        if (now()->isBefore(now()->parse($date))) {
                            $search['label'] = [
                                'name' => 'PRE-ORDER',
                                'color' => '#BB1E1E',
                            ];
                        } elseif ($date = now()->parse($date)->isAfter(now()->subDays(30 * 5))) {
                            $search['label'] = [
                                'name' => 'New'
                                    . (
                                        data_get($search, 'creators') == '{}' || data_get($search, 'creators') == null
                                            ? ''
                                            :' Release'
                                    ),
                                'color' => '#0C9E32',
                            ];
                        }
                    }
                    return $search;
                });

                $breadcrumbs = [];

                if (!empty($request->q)) {
                    $total = $products->total();
                    $per_page = $products->perPage();
                    $current_page = $products->currentPage();

                    $breadcrumbs = [[
                        'name' => (($current_page - 1) * $per_page) + 1 . " - " . min(($current_page * $per_page), $total) . " of {$total} results for: \"{$request->q}\""
                    ]];
                }

                return [
                    'products' => $products,
                    ...$facets,
                    'breadcrumbs' => $breadcrumbs,
                ];
            }
        );
    }

    public function suggestion(Request $request)
    {
        return Cache::tags(['search'])->remember(
            "suggestion_{$request->q}",
            60 * 60 * 24 * 3,
            function () use ($request) {
                $options = [
                    'result_fields' => [
                        'title' => [
                            'snippet' => [
                                'size' => 100,
                                'fallback' => true,
                            ]
                        ],
                        'vendor' => [
                            'snippet' => [
                                'size' => 100,
                                'fallback' => true,
                            ]
                        ],
                    ],
                ];
                return Product::search($request->q)->get();
            }
        );
    }
}
