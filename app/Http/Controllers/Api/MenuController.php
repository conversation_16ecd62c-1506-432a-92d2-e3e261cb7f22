<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Outl1ne\MenuBuilder\Models\Menu;
use Outl1ne\MenuBuilder\Models\MenuItem;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class MenuController extends Controller
{
    public function children(MenuItem $item)
    {
        return Cache::remember('menu_' . $item->id, 60 * 60 * 24 * 27, function () use ($item) {
            $images = DB::table('media')
                ->whereIn('model_id', $item->children->pluck('value'))
                ->where('model_type', 'App\\Page')
                ->orWhere('model_type', 'App\\Category')
                ->orWhere('model_type', 'App\\AutomatedCategory')
                ->get();

            return $item->children->map(function ($item) use ($images) {
                if (!$item->enabled) {
                    return;
                }
                $paths = [
                    'App\\Classes\\Pages',
                    'App\\Classes\\Categories',
                    'App\\Classes\\AutomatedCategories',
                ];
                switch ($item->class) {
                    case 'App\\Classes\\Pages':
                        $class = 'App\\Page';
                        break;

                    case 'App\\Classes\\Categories':
                        $class = 'App\\Category';
                        break;
                    case 'App\\Classes\\AutomatedCategories':
                        $class = 'App\\AutomatedCategory';
                        break;

                    default:
                        $class = null;
                        break;
                }
                if ($class == 'App\\Category') {
                    $path = '/categories/' . str_slug($item->name) . '/' . $item->value;
                } elseif ($class == 'App\\AutomatedCategory') {
                    $path = '/a/categories/' . str_slug($item->name) . '/' . $item->value;
                } else {
                    $path = '/' . str_slug($item->name) . '/' . $item->value;
                }

                if (in_array($item->class, $paths)) {
                    $image = collect($images->filter(function ($image) use ($class, $item) {
                        return $image->model_type == $class
                            && $image->collection_name == 'media'
                            && $image->model_id == $item->value;
                    }))->first();
                    if ($image) {
                        // $image = env('CLOUDFRONT_URL') . '/media/' . $image->id . '/' . $image->file_name;
                        // $image = env('CLOUDFRONT_URL') . '/media/' . $image->id . '/' . $image->file_name;
                        $image = Media::find(data_get($image, 'id'))->getUrl('thumb');
                    }

                    return array_merge(
                    // $item->toArray(),
                    // ['image' => $image],
                    // ['path' => $path]
                        ['path' => $path],
                        ['image' => $image],
                        ['name' => $item->name],
                        ['class' => $item->class]
                    );
                }
                return $item;
            })->filter()->values();
        });
    }

    public function departments()
    {
        return Cache::remember('department', 60 * 60 * 24 * 27, function () {
            $id = Menu::where('slug', 'departments')
                ->firstOrFail()->id;

            $children = MenuItem::where('menu_id', $id)
                ->where('parent_id', null)
                ->where('enabled', 1)
                ->orderBy('order', 'asc')->get();

            $images = DB::table('media')
                ->whereIn('model_id', $children->pluck('value'))
                ->where('model_type', 'App\\Page')
                ->orWhere('model_type', 'App\\Category')
                ->orWhere('model_type', 'App\\AutomatedCategory')
                ->get();

            return $children->map(function ($item) use ($images) {
                $paths = [
                    'App\\Classes\\Pages',
                    'App\\Classes\\Categories',
                    'App\\Classes\\AutomatedCategories',
                ];
                switch ($item->class) {
                    case 'App\\Classes\\Pages':
                        $class = 'App\\Page';
                        break;

                    case 'App\\Classes\\Categories':
                        $class = 'App\\Category';
                        break;

                    case 'App\\Classes\\AutomatedCategories':
                        $class = 'App\\AutomatedCategory';
                        break;

                    default:
                        $class = null;
                        break;
                }
                if ($class == 'App\\Category') {
                    $path = '/categories/' . str_slug($item->name) . '/' . $item->value;
                } elseif ($class == 'App\\AutomatedCategory') {
                    $path = '/a/categories/' . str_slug($item->name) . '/' . $item->value;
                } else {
                    $path = '/' . str_slug($item->name) . '/' . $item->value;
                }

                if (in_array($item->class, $paths)) {
                    $image = collect($images->filter(function ($image) use ($class, $item) {
                        return $image->model_type == $class
                            && $image->collection_name == 'media'
                            && $image->model_id == $item->value;
                    }))->first();
                    if ($image) {
                        // $image = env('AWS_URL') . '/media/' . $image->id . '/' . $image->file_name;
                        $image = Media::find(data_get($image, 'id'))->getUrl('thumb');
                    }

                    return array_merge(
                    // $item->toArray(),
                        ['path' => $path],
                        ['image' => $image],
                        ['name' => $item->name],
                        ['class' => $item->class]
                    );
                }
                return $item;
            });
        });
    }
}
