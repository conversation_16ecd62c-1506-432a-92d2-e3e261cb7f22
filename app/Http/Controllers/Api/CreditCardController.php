<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use umTransaction;

include storage_path('includes/epay/master/usaepay.php');

//https://help.usaepay.info/developer/sdks/php/
class CreditCardController extends Controller
{
    public static function AuthorizeCC($order, $amount = null)
    {
        $tran = self::GetTran();

        $tran->command = 'authonly';

        $tran->card = $order->payment->token;

        $tran->exp = '0000';

        $tran->amount = $amount ?? $order->grand_total;
        $tran->invoice = $order->id;

        $tran->cardholder = data_get($order, 'payments.creditInfo.name');
        $tran->street = data_get($order, 'payments.creditInfo.address_line_1');
        $tran->zip = data_get($order, 'payments.creditInfo.postal_code');
        $tran->description = "Online Order";
        $tran->cvv2 = $order->payment->security_code;

        $result = self::ProccessAndReturn($tran);
        $result = forceArray($result);

        if ($result['Message'] == 'Request Approved') {
            $order->payment->update([
                'refnum' => $result['RefNum'],
                'authorized' => $result,
                'authorized->Date' => now(),
                'authorized->Amount' => $amount,
                'status' => 'Charged'
            ]);

            $order->update([
                'status' => 'paid',
                'payment_status' => 'authorized'
            ]);

            return $result;
        } else {
            $errors = $order->payment->errors ?? [];
            $errors['date'] = now()->toDateTimeString();
            $errors['command'] = 'authonly';
            $errors[] = $result;
            $order->payment->update([
                'errors' => $errors,
                'authorized->Date' => now()->ToDateTimeString(),
                'authorized->Message' => $result['Reason'],
            ]);
            $order->update([
                'status' => 'cancelled',
                'payment_status' => 'declined'
            ]);
            //   if ($subscription = \App\Subscription::find(data_get($order, 'meta.subscription.id'))) {
            //             $subscription->update([
            //                 'status' => 'declined'
            //             ]);
            //   }

            EmailsController::SendPaymentError($order);

            abort(400, $result['Reason']);
        }
    }

    private static function GetTran()
    {
        $tran = new umTransaction;

        // $tran->cabundle = '/Users/<USER>/Downloads/cacert.pem';

        $tran->key = (env('EPAY_KEY'));
        $tran->pin = (env('EPAY_PIN'));
        // $tran->key = '_2NRqj4y6CF9fbOesg4kNhin57Ps0bxL';
        // $tran->pin = '1234';

        //$tran->ip = $REMOTE_ADDR; // This allows fraud blocking on the customers ip address ----- WHAT IS THIS???

        if($tran->pin == '1234') {
            $tran->usesandbox = true;
            $tran->testmode = 1;
        } else {
            $tran->usesandbox = false;
            $tran->testmode = 0;
        }

        return $tran;
    }

    private static function ProccessAndReturn($tran)
    {
        //flush();

        if ($tran->Process()) {
            return [
                'Message' => 'Request Approved',
                'Authcode' => $tran->authcode,
                'RefNum' => $tran->refnum,
                'AVS Result' => $tran->avs_result,
                'Cvv2 Result' => $tran->cvv2_result,
                'Token' => $tran->cardref
            ];
        } else {
            return
                [
                    'Message' => 'Card Declined',
                    'Reason' => $tran->error,
                    'Curl Error' => @$tran->curlerror ? $tran->curlerror : ''
                ];
        }
    }

    public static function Charge($order, $amount = null)
    {
        $tran = self::GetTran();

        $tran->command = 'cc:sale';

        $tran->card = $order->payment->token;

        $tran->exp = '0000';

        $tran->amount = $amount ?? $order->grand_total;
        $tran->invoice = $order->id;

        $tran->cardholder = data_get($order, 'payments.creditInfo.name');
        $tran->street = data_get($order, 'payments.creditInfo.address_line_1');
        $tran->zip = data_get($order, 'payments.creditInfo.postal_code');
        $tran->description = "Online Order";
        $tran->cvv2 = $order->payment->security_code;

        $result = self::ProccessAndReturn($tran);

        $result = forceArray($result);

        if ($result['Message'] == 'Request Approved') {
            $charges = $order->payment->charges ?? [];

            array_push($charges, [
                'Date' => now(),
                'RefNum' => $result['RefNum'],
                'Result' => $result,
                'Authcode' => $result['Authcode'],
                'Amount' => $amount
            ]);

            $order->payment->update([
                'charges' => $charges,
                'status' => 'Charged',
                'refnum' => $result['RefNum'],
            ]);
            $order->update([
                'status' => 'paid',
                'payment_status' => 'paid'
            ]);

            return $result;
        } else {
            $order->update([
                'status' => 'cancelled',
                'payment_status' => 'declined'
            ]);

            $errors = $order->payment->errors ?? [];
            $errors['date'] = now()->toDateTimeString();
            $errors['command'] = 'cc:sale';
            $errors[] = $result;
            $order->payment->update(['errors' => $errors]);
            //   if ($subscription = \App\Subscription::find(data_get($order, 'meta.subscription.id'))) {
            //             $subscription->update([
            //                 'status' => 'declined'
            //             ]);
            //   }

            EmailsController::SendPaymentError($order);

            abort(400, $result['Reason']);
        }
    }

    public static function Capture($order)
    {
        $tran = self::GetTran();

        $tran->command = 'capture';

        $tran->refnum = $order->payment->refnum;

        $result = self::ProccessAndReturn($tran);
        $result = forceArray($result);

        if ($result['Message'] == 'Request Approved') {
            $captures = $order->payment->captures ?? [];

            array_push($captures, [
                'Date' => now(),
                'RefNum' => $result['RefNum'],
                'Result' => $result,
                'Authcode' => $result['Authcode']
            ]);

            $order->payment->update([
                'captures' => $captures,
                'status' => 'Charged'
            ]);
        } else {
            $errors = $order->payment->errors ?? [];
            $errors['date'] = now()->toDateTimeString();
            $errors['command'] = 'capture';
            $errors[] = $result;
            $order->payment->update(['errors' => $errors]);
        }

        return $result['Message'] == 'Request Approved';
    }

    public static function Refund($order, $amount)
    {
        $tran = self::GetTran();
        $tran->card = $order->payment->token;

        $tran->exp = '0000';

        $tran->command = 'refund'; // 'credit';
        $tran->refnum = $order->payment->refnum;

        $tran->amount = $amount;
        $tran->invoice = $order->id;

        $tran->cardholder = data_get($order, 'payments.creditInfo.name');
        $tran->street = data_get($order, 'payments.creditInfo.address_line_1');
        $tran->zip = '';
        data_get($order, 'payments.creditInfo.postal_code');
        $tran->description = "Online Order";
        $tran->cvv2 = $order->payment->security_code;

        $result = self::ProccessAndReturn($tran);
        $result = forceArray($result);

        if ($result['Message'] == 'Request Approved') {
            $refunds = $order->payment->refunds ?? [];

            array_push($refunds, [
                'date' => now(),
                'RefNum' => $result['RefNum'],
                'Result' => $result,
                'Authcode' => $result['Authcode'],
                'Amount' => $amount
            ]);

            $order->payment->update([
                'refunds' => $refunds,
            ]);
        } else {
            $errors = $order->payment->errors ?? [];
            $errors['date'] = now()->toDateTimeString();
            $errors['command'] = 'refund';
            $errors[] = $result;
            $order->payment->update(['errors' => $errors]);
        }

        return $result;
    }

    public static function Cancel($order)
    {
        $tran = self::GetTran();

        $tran->command = 'creditvoid';

        $tran->refnum = $order->payment->refnum;

        $result = self::ProccessAndReturn($tran);
        $result = forceArray($result);

        if ($result['Message'] == 'Request Approved') {
            $order->payment->update([
                'status' => 'Cancelled'
            ]);
        } else {
            $errors = $order->payment->errors ?? [];
            $errors['date'] = now()->toDateTimeString();
            $errors['command'] = 'creditvoid';
            $errors[] = $result;
            $order->payment->update(['errors' => $errors]);
        }
    }

    public static function CancelViaRefnum($refnum)
    {
        $tran = self::GetTran();

        $tran->command = 'creditvoid';

        $tran->refnum = $refnum;

        $result = self::ProccessAndReturn($tran);
        $result = forceArray($result);
    }

    public function GetToken(Request $request)
    {
        $tran = self::GetTran();

        $tran->command = "cc:save";

        $tran->card = $request->card;
        $tran->exp = $request->exp;

        //flush();

        if ($tran->Process()) {
            return [
                'Message' => 'Request Approved',
                'Authcode' => $tran->authcode,
                'RefNum' => $tran->refnum,
                'AVS Result' => $tran->avs_result,
                'Cvv2 Result' => $tran->cvv2_result,
                'Token' => $tran->cardref
            ];
        } else {
            return response()->json([
                'Message' => 'Card Declined',
                'Reason' => $tran->error,
                'Curl Error' => @$tran->curlerror ? $tran->curlerror : ''
            ], 404);
        }
    }
}
