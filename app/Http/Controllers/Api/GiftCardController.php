<?php

namespace App\Http\Controllers\Api;

use App\GiftCard;
use App\Http\Controllers\Controller;
use App\PosMessage;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\Request;

class GiftCardController extends Controller
{
    public static function createVoucher($transaction)
    {
        $giftCard = $transaction->giftCard;
        try {
            $client = new Client;

            $response = $client->post("https://api5.firstchoicepos.com/v1/Voucher/Create", [
                'headers' => ['Authorization' => 'Basic ' . env('POS')],
                'json' => [
                    'VoucherNumber' => $giftCard->code,
                    'RefillAmount' => $giftCard->amount,
                    'ItemLookupCode' => 'GC',
                    'SiteId' => 1,
                    'ClosingWorkflowStateId' => 139,
                    'TransactionNumber' => 'E-' . $giftCard->id,
                ]
            ]);

            $giftCard->update([
                'meta->pos' => 'updated'
            ]);
            $transaction->update(['pos_message' => $response->getBody()->getContents()]);

            return $response->getBody()->getContents();
        } catch (Exception $ex) {
            $result = $ex->getResponse()->getBody()->getContents();
            $message = data_get(forceArray($result), 'Message');
            $giftCard->update([
                'meta->pos' => $message
            ]);

            $transaction->update(['pos_message' => $message]);

            PosMessage::create([
                'model_type' => 'App\GiftCard',
                'model_id' => $giftCard->id,
                'action' => 'Create Voucher',
                'message' => $message,
                'status' => 'Unhandled'
            ]);
        }
    }

    public static function voucherPayment($code, $amount, $order, $transaction)
    {
        $code = $transaction->giftCard->code;
        $order = $transaction->order;

        try {
            $client = new Client;

            $response = $client->post("https://api5.firstchoicepos.com/v1/Voucher/UseForPayment", [

                'headers' => ['Authorization' => 'Basic ' . env('POS')],
                'json' => [
                    'VoucherNumber' => $code,
                    'ChargeAmount' => $amount,
                    'SiteId' => 1,
                    'PaymentNumber' => substr($code, -4) . '_' . substr($order->id, -4),
                    'TenderName' => 'Voucher',
                ]
            ]);

            $order->update([
                "meta->pos_gift_card->{$code}" => $response->getBody()->getContents()
            ]);
            $transaction->update(['pos_message' => $response->getBody()->getContents()]);

            return $response->getBody()->getContents();
        } catch (Exception $ex) {
            $result = $ex->getResponse()->getBody()->getContents();
            $message = data_get(forceArray($result), 'Message');
            $order->update([
                "meta->pos_gift_card->{$code}" => $message,
                'status' => 'cancelled',
                'payment_status' => 'declined',
            ]);
            $transaction->update(['pos_message' => $message]);

            $giftCard = GiftCard::firstWhere('code', $code);
            PosMessage::create([
                'model_type' => 'App\GiftCard',
                'model_id' => $giftCard->id,
                'action' => 'Voucher Payment',
                'pos_message' => $message,
                'status' => 'Unhandled'
            ]);
            abort(400, 'Gift Card Revoked');
        }
    }

    public static function voucherVoid($giftCard)
    {
        $balance = (new GiftCardController)->checkGiftCard(
            request()->merge(['code' => $giftCard->code])
        );
        try {
            $client = new Client;

            $response = $client->post("https://api5.firstchoicepos.com/v1/Voucher/UseForPayment", [

                'headers' => ['Authorization' => 'Basic ' . env('POS')],
                'json' => [
                    'VoucherNumber' => $giftCard->code,
                    'ChargeAmount' => $balance,
                    'SiteId' => 1,
                    'PaymentNumber' => substr($giftCard->code, -4) . '_VOID' . '_' . rand(100, 999),
                    'TenderName' => 'Voucher',
                ]
            ]);
            $message = $response->getBody()->getContents();
            return $message;
        } catch (Exception $ex) {
            $result = $ex->getResponse()->getBody()->getContents();
            $message = data_get(forceArray($result), 'Message');

            PosMessage::create([
                'model_type' => 'App\GiftCard',
                'model_id' => $giftCard->id,
                'action' => 'Voucher Void',
                'message' => $message,
                'status' => 'Unhandled'
            ]);
        }

        $giftCard->transactions()->create([
            'gift_card_id' => $giftCard->id,
            'amount' => $balance,
            'type' => 'Void',
            'message' => $message
        ]);
        $giftCard->update([
            'balance' => 0.00
        ]);
    }

    public function checkGiftCard(Request $request)
    {
        $balance = null;
        try {
            $client = new Client;

            $response = $client->get("https://api5.firstchoicepos.com/v1/Voucher/Get/{$request->code}", [
                'headers' => ['Authorization' => 'Basic ' . env('POS')],
            ]);

            $voucher = $response->getBody()->getContents();

            $balance = data_get(forceArray($voucher), 'Balance');

            $giftCard = GiftCard::get()->firstWhere('code', $request->code);

            if ($giftCard) {
                $giftCard->update([
                    'balance' => $balance
                ]);
            } else {
                GiftCard::create([
                    'balance' => $balance,
                    'code' => data_get(forceArray($voucher), 'VoucherNumber'),
                    'amount' => data_get(forceArray($voucher), 'TotalValue'),
                    'from_name' => 'POS'
                ]);
            }
            // return $balance;
        } catch (Exception $ex) {
            // $card = GiftCard::getCard($request->code);

            // return $card->balance;
        }
        if (is_null($balance)) {
            abort(400, 'Not valid gift card');
        }
        if (!$balance) {
            abort(400, 'Your balance is empty');
        }
        return $balance;
    }

    public static function refillVoucher($transaction)
    {
        $giftCard = $transaction->giftCard;
        $amount = $transaction->amount;
        $order = $transaction->order;

        try {
            $client = new Client;

            $transactionNumber = collect([
                $giftCard->id,
                $order->id,
                \App\GiftCardTransaction::where([
                    'order_id'=> $order->id,
                    'gift_card_id' => $giftCard->id,
                ])->count(),
            ])
            ->filter()
            ->join('_');

            $response = $client->post("https://api5.firstchoicepos.com/v1/Voucher/Refill", [
                'headers' => ['Authorization' => 'Basic ' . env('POS')],
                'json' => [
                    'VoucherNumber' => $giftCard->code,
                    'RefillAmount' => $amount,
                    'ItemLookupCode' => 'GC',
                    'SiteId' => 1,
                    'ClosingWorkflowStateId' => 139,
                    'TransactionNumber' => 'E-' . $transactionNumber,
                ]
            ]);

            $giftCard->update([
                'meta->pos_refill' => $response->getBody()->getContents()
            ]);
            $transaction->update(['pos_message' => $response->getBody()->getContents()]);


            return $response->getBody()->getContents();
        } catch (Exception $ex) {
            $result = $ex->getResponse()->getBody()->getContents();
            $message = data_get(forceArray($result), 'Message');
            $giftCard->update([
                'meta->pos_refill' => $message
            ]);
            $transaction->update(['pos_message' => $message]);

            PosMessage::create([
                'model_type' => 'App\GiftCard',
                'model_id' => $giftCard->id,
                'action' => 'Refill Voucher',
                'message' => $message,
                'status' => 'Unhandled'
            ]);
        }
    }
}
