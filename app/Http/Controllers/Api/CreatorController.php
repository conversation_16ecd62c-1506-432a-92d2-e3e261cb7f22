<?php

namespace App\Http\Controllers\Api;

use App\Creator;
use App\Http\Controllers\Controller;
use App\Traits\FacetTrait;
use Illuminate\Http\Request;

class CreatorController extends Controller
{
    use FacetTrait;

    public function products(Request $request, $slug, Creator $creator)
    {
        if (!$request->q) {
            $request = $request->merge(['q' => ' ']);
        }
        $request->merge(['all_creators' => $creator->name]);
        return collect((new SearchController)->index($request, collect(['and_creators' => [$creator->name]])))->merge([
            'breadcrumbs' => [
                ['name' => 'Home', 'path' => '/'],
                ['name' => $creator->name],
            ],
            'description' => $creator->description
        ]);
    }
}
