<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Page;
use Illuminate\Support\Facades\Cache;

class PagesController extends Controller
{
    public function index()
    {
        return Cache::tags(['pages'])->remember('pages', 60 * 60 * 3, function () {
            return optional(Page::where('name', 'Home')->first())->load('media');
        });
    }

    public function show($slug, Page $page = null)
    {
        if (!$page) {
            $page = Page::where('slug', $slug)->firstOrFail();
        }

        if (str_slug($page->title) != $slug && !request()->header('app')) {
            abort(302, $page->path);
        }

        return Cache::tags(['pages'])->remember("page_{$page->id}", 60 * 60 * 24 * 7, function () use ($page) {
            return $page->load('media');
        });
    }
}
