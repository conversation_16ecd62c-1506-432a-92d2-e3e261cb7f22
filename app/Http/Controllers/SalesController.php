<?php

namespace App\Http\Controllers;

use App\Product;
use App\Sale;
use Illuminate\Support\Facades\Cache;

class SalesController extends Controller
{
    public static function ApplySales()
    {
        self::StartSales();
        self::EndSales();
    }

    public static function StartSales()
    {
        $sales = Sale::whereDate('start', now())->get();
        $productIds = $sales->map(function ($sale) {
            return $sale->model->product_id;
        });

        collect($productIds)->each(function ($id) {
            Cache::forget("product_{$id}");
        });

        $productIds = $productIds->flatten()->unique()->values()->toArray();
        dispatch(function () use ($productIds) {
            Product::isSearchable()->find($productIds)->map->updateSearchFeild();
            idsToSwiftype($productIds);
        });
    }

    public static function EndSales()
    {
        $sales = Sale::whereDate('end', now())->get();
        $productIds = $sales->map(function ($sale) {
            return $sale->model->product_id;
        });
        $sales->each->delete();

        collect($productIds)->each(function ($id) {
            Cache::forget("product_{$id}");
        });

        $productIds = $productIds->flatten()->unique()->values()->toArray();
        dispatch(function () use ($productIds) {
            Product::isSearchable()->find($productIds)->map->updateSearchFeild();
            idsToSwiftype($productIds);
        });
    }
}
