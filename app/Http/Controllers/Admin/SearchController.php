<?php

namespace App\Http\Controllers\Admin;

use App\Bundle;
use App\Category;
use App\Creator;
use App\Customer;
use App\Filter;
use App\Group;
use App\Http\Controllers\Controller;
use App\Label;
use App\Product;
use App\Vendor;

class SearchController extends Controller
{
    public static function products()
    {
        $q = request()->q;
        return Product::setEagerLoads(
            [
                'variationInfos' => function ($query) {
                    $query;
                }
            ]
        )
            ->where('id', $q)
            ->orWhere('title', 'LIKE', '%' . $q . '%')
            ->orWhere('sku', 'LIKE', '%' . $q . '%')
            ->limit(25)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'product_id' => $item->id,
                    'type' => 'product',
                    'title' => $item->title,
                    'price' => $item->toArray()['price'],
                    'media' => $item->getFirstMediaUrl('media'),
                    'sku' => $item->sku,
                    'max' => $item->max,
                    'variations' => $item->variationInfos
                        ->map(function ($variation) use ($item) {
                            return [
                                'id' => $variation->id,
                                'type' => 'variation',
                                'max' => $item->max,
                                'title' => $item->title,
                                'price' => $variation->toArray()['price'],
                                'media' => $item->getFirstMediaUrl('media'),
                                'meta' => json_decode($variation->meta),
                                'sku' => $item->sku,
                                'product_id' => $item->id,
                            ];
                        }),
                ];
            });
    }

    public static function only_products()
    {
        $q = request()->q;

        return Product::setEagerLoads([])
            ->where('id', $q)
            ->orWhere('title', 'LIKE', '%' . $q . '%')
            ->orWhere('sku', 'LIKE', '%' . $q . '%')
            ->limit(25)
            ->get()
            ->merge(
                Bundle::setEagerLoads([])
                    ->where('id', $q)
                    ->orWhere('title', 'LIKE', '%' . $q . '%')
                    ->orWhere('sku', 'LIKE', '%' . $q . '%')
                    ->limit(25)
                    ->get()
            )->map(function ($item) {
                return [
                    'id' => $item->id,
                    'title' => $item->title,
                    'price' => $item->price,
                    'media' => $item->getFirstMediaUrl('media'),
                    'sku' => $item->sku,
                ];
            });
    }

    public static function categories()
    {
        $q = request()->q;
        return Category::setEagerLoads([])
            ->where('id', $q)
            ->orWhere('name', 'LIKE', '%' . $q . '%')
            ->limit(25)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'title' => $item->name,
                    'media' => $item->getFirstMediaUrl('media'),
                ];
            });
    }

    public static function vendors()
    {
        $q = request()->q;
        return Vendor::setEagerLoads([])
            ->where('id', $q)
            ->orWhere('name', 'LIKE', '%' . $q . '%')
            ->limit(25)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'title' => $item->name,
                    'media' => $item->getFirstMediaUrl('media'),
                ];
            });
    }

    public static function filters()
    {
        $q = request()->q;
        return Filter::setEagerLoads([])
            ->where('id', $q)
            ->orWhere('name', 'LIKE', '%' . $q . '%')
            ->limit(25)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'title' => $item->name,
                ];
            });
    }

    public static function customers()
    {
        $q = request()->q;
        return Customer::setEagerLoads([])
            ->where('id', $q)
            ->orWhere('name', 'LIKE', '%' . $q . '%')
            ->orWhere('email', 'LIKE', '%' . $q . '%')
            ->limit(25)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'title' => $item->name,
                ];
            });
    }

    public static function groups()
    {
        $q = request()->q;
        return Group::setEagerLoads([])
            ->where('id', $q)
            ->orWhere('name', 'LIKE', '%' . $q . '%')
            ->limit(25)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'title' => $item->name,
                ];
            });
    }

    public static function creator_delayed()
    {
        if (request('type') == 'Vendors') {
            return self::vendor_delayed();
        }
        $request = request();
        $q = $request->q;

        if ($q != '') {
            $creators = Creator::setEagerLoads([])
                ->where('id', $q)
                ->orWhere('name', 'LIKE', '%' . $q . '%');
            if (collect((new Creator)->options)->keys()->contains(strtolower($request->type))) {
                // since authors & illustrator are for books, we combine them
                if (in_array(strtolower($request->type), ['author', 'illustrator'])) {
                    $creators = $creators
                        ->where('type', 'author')
                        ->orWhere('type', 'illustrator');
                } else {
                    $creators = $creators->where('type', strtolower($request->type));
                }
            }
            $creators = $creators->limit(15)
                ->get()
                ->map(function ($item) {
                    return [
                        'value' => $item->id,
                        'display' => $item->name,
                    ];
                });
        } else {
            $creators = collect();
        }

        if ($id = $request->product_id) {
            $db_creators = Product::find($id)->creators();
            if (in_array(strtolower($request->type), ['author', 'illustrator'])) {
                $db_creators = $db_creators
                    ->where('type', 'author')
                    ->orWhere('type', 'illustrator');
            } else {
                $db_creators = $db_creators
                    ->where('type', strtolower($request->type));
            }

            $creators = collect($creators)
                ->merge(
                    $db_creators->get()
                        ->map(function ($item) {
                            return ['value' => $item->id, 'display' => $item->name];
                        })
                );
        }
        return $creators;
    }

    public static function vendor_delayed()
    {
        $request = request();
        $q = $request->q;

        $collection = Vendor::setEagerLoads([])
            ->where('id', $q)
            ->orWhere('name', 'LIKE', '%' . $q . '%')
            ->limit(15)
            ->get()
            ->map(function ($item) {
                return [
                    'value' => $item->id,
                    'display' => $item->name,
                ];
            });

        if (
            !$collection->contains('value', $request->model_id)
            && $vendor = Vendor::setEagerLoads([])->find($request->model_id)
        ) {
            $collection = collect($collection)->merge([
                ['value' => $vendor->id, 'display' => $vendor->name]
            ]);
        }

        return $collection->reverse()->values();
    }

    public static function creators()
    {
        $q = request()->q;
        return Creator::setEagerLoads([])
            ->where('id', $q)
            ->orWhere('name', 'LIKE', '%' . $q . '%')
            ->limit(25)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'title' => $item->name,
                    'media' => $item->getFirstMediaUrl('media'),
                ];
            });
    }

    public static function filter_delayed()
    {
        $request = request();
        $q = $request->q;
        $collection = Filter::setEagerLoads([])
            ->where('id', $q)
            ->orWhere('name', 'LIKE', '%' . $q . '%')
            ->limit(15)
            ->get()
            ->map(function ($item) {
                return [
                    'value' => $item->id,
                    'display' => $item->name,
                ];
            });

        if (
            !$collection->contains('value', $request->model_id)
            && $filter = Filter::setEagerLoads([])->find($request->model_id)
        ) {
            $collection = collect($collection)->merge([
                ['value' => $filter->id, 'display' => $filter->name]
            ]);
        }

        return $collection->reverse()->values();
    }

    public static function label_delayed()
    {
        $request = request();
        $q = $request->q;
        $collection = Label::setEagerLoads([])
            ->where('id', $q)
            ->orWhere('name', 'LIKE', '%' . $q . '%')
            ->limit(15)
            ->get()
            ->map(function ($item) {
                return [
                    'value' => $item->id,
                    'display' => $item->name,
                ];
            });

        if (
            !$collection->contains('value', $request->model_id)
            && $label = Label::setEagerLoads([])->find($request->model_id)
        ) {
            $collection = collect($collection)->merge([
                ['value' => $label->id, 'display' => $label->name]
            ]);
        }

        return $collection->reverse()->values();
    }
}
