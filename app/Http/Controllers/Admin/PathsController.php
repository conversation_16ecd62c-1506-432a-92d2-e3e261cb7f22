<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Capitalc\Creators\Creators;
use Capitalc\Paths\Paths;
use Illuminate\Support\Facades\DB;
use Laravel\Nova\Fields\Hidden;
use Laravel\Nova\Fields\Select;

class PathsController extends Controller
{
    public static function index()
    {
        $array = [];
        $array[] = [
            'label' => 'Categories',
            'id' => 'Categories',
            'children' => DB::table('categories')->get(['id', 'name'])
                ->map(function ($item) {
                    return [
                        'id' => "/categories/" . str_slug($item->name) . "/{$item->id}",
                        'name' => $item->name,
                        'label' => $item->name,
                    ];
                })
        ];
        $array[] = [
            'label' => 'Automated Categories',
            'id' => 'Automated Categories',
            'children' => DB::table('automated_categories')->get(['id', 'name'])
                ->map(function ($item) {
                    return [
                        'id' => "/a/categories/" . str_slug($item->name) . "/{$item->id}",
                        'name' => $item->name,
                        'label' => $item->name,
                    ];
                })
        ];

        $array[] = [
            'label' => 'Vendors',
            'id' => 'Vendors',
            'children' => DB::table('vendors')->get(['id', 'name'])
                ->map(function ($item) {
                    return [
                        'id' => "/vendor/" . str_slug($item->name) . "/{$item->id}",
                        'name' => $item->name,
                        'label' => $item->name,
                    ];
                })
        ];

        $array[] = [
            'label' => 'Creators',
            'id' => 'Creators',
            'children' => DB::table('creators')->get(['id', 'name'])
                ->map(function ($item) {
                    return [
                        'id' => "/creator/" . str_slug($item->name) . "/{$item->id}",
                        'name' => $item->name,
                        'label' => $item->name,
                    ];
                })
        ];
        $array[] = [
            'label' => 'Pages',
            'id' => 'Pages',
            'children' => DB::table('pages')->get(['id', 'name', 'title'])
                ->map(function ($item) {
                    return [
                        'id' => "/" . str_slug($item->title) . "/{$item->id}",
                        'name' => $item->name,
                        'label' => $item->name,
                    ];
                })
        ];

        $array[] = [
            'label' => 'Custom',
            'id' => 'custom',
        ];

        return $array;
    }

    public static function novaComponents()
    {
        return [
            Paths::make('Link')->withMeta([
                'options' => self::index()
            ]),
            Hidden::make('Filters'),

            Creators::make('Tags', 'tags_ids')
                ->withMeta([
                    'extraAttributes' => [
                        'label' => 'Tags',
                        'type' => '',
                        'availableResources' => DB::table('tags')->get(['name', 'id'])
                            ->map(function ($item) {
                                return ['display' => data_get(json_decode($item->name), 'en'), 'value' => $item->id];
                            })->values()->toArray(),
                    ]
                ]),

            Select::make('Status')->options([
                ['label' => 'New', 'value' => 'new'],
                ['label' => 'Sale', 'value' => 'sale'],
            ])->nullable()


        ];
    }

    public static function vendors()
    {
        if (request()->is('nova-api/pages')) {
            return [];
        }
        return DB::table('vendors')->get(['name', 'id'])
            ->map(function ($item) {
                return ['display' => $item->name, 'value' => $item->id];
            });
    }

    public static function relationships()
    {
        return;
    }
}
