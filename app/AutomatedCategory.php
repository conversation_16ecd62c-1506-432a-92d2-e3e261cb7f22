<?php

namespace App;

use <PERSON><PERSON>\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class AutomatedCategory extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    protected $casts = [
        'categories_and' => 'array',
        'categories_or' => 'array',
        'categories_exclude' => 'array',
        'filters_and' => 'array',
        'filters_or' => 'array',
        'filters_exclude' => 'array',
        'vendors_and' => 'array',
        'vendors_or' => 'array',
        'vendors_exclude' => 'array',
        'creators_and' => 'array',
        'creators_or' => 'array',
        'creators_exclude' => 'array',
        'tags_and' => 'array',
        'tags_or' => 'array',
        'tags_exclude' => 'array',
        'labels_and' => 'array',
        'labels_or' => 'array',
        'labels_exclude' => 'array',
    ];

    public function getPathAttribute()
    {
        return "/a/categories/" . str_slug($this->name) . "/{$this->id}";
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image')
            ->singleFile();
    }
}
