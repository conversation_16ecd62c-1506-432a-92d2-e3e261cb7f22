<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\Api\EmailsController;

class Order extends Model
{
    protected $guarded = [];

    public $casts = [
        'products' => 'array',
        'shipping' => 'array',
        'payments' => 'array',
        'meta' => 'array',
        'product_ids' => 'array',
        'discount' => 'array',
        'reversions' => 'array',
    ];

    protected $appends = [
        'email',
        'charge',
        'shippable',
        'return_by',
        'sub_total_left',
        'total_refund',
        'shippes'
    ];
    protected $with = [
        'returns',
        'payment',
    ];

    public function getChargeAttribute()
    {
        return optional($this->payment)->total ?? 0;
    }

    public function getChargeLeftAttribute()
    {
        return optional($this->payment)->total - optional($this->payment)->refund ?? 0;
    }

    public function scopeShippable($query)
    {
        $query
            ->whereRaw('JSON_CONTAINS(products->"$[*]",JSON_OBJECT("item_type","physical"))')
            ->orWhereRaw('JSON_CONTAINS(products->"$[*]",JSON_OBJECT("item_type","both"))');

        return $query;
    }

    public function getShippableAttribute()
    {
        return collect($this->products)->map(function ($product) {
            return data_get($product, 'item_type') == 'physical' || data_get($product, 'item_type') == 'both';
        })->contains(true);
    }

    public function HasNonCancellableItems()
    {
        return collect($this->products)->map(function ($product) {
            return data_get($product, 'item_type') != 'physical' || data_get($product, 'personalization');
        })->contains(true);
    }

    public function getSubTotalLeftAttribute()
    {
        $returnsTotal = $this->returns->pluck('sub_total')->sum() ?? 0;
        $cancelTotal = collect($this->products)->map(function ($product) {
            if (data_get($product, 'status') == 'cancelled') {
                return data_get($product, 'price') * data_get($product, 'quantity');
            }
        })->sum() ?? 0;
        return (float)$this->sub_total - (float)$returnsTotal - (float)$cancelTotal;
    }

    public function returns()
    {
        return $this->hasMany(Returns::class);
    }

    public function getReturnByAttribute()
    {
        $order = $this;

        $cutt_off = data_get($order, 'shipping.tracking.arrived_at')
            ?? data_get($order, 'shipping.tracking.arrival_date')
            ?? data_get($order, 'shipping.shippingType.estimated_arrival')
            ?? $order->created_at;

        return now()->parse($cutt_off)->addDays((int)settings()->getValue('return_expires'));
    }

    public function splitShipments()
    {
        return $this->hasMany(SplitShipment::class);
    }

    public function getShipmentsAttribute()
    {
        return $this->splitShipments->count() ? $this->splitShipments : [['products' => $this->products]];
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function discount()
    {
        return $this->belongsTo(Discount::class);
    }

    public function giftCards()
    {
        return $this->hasMany(GiftCard::class);
    }

    public function giftCardTransactions()
    {
        return $this->hasMany(GiftCardTransaction::class);
    }

    public function giftNotes()
    {
        return $this->hasMany(GiftNote::class);
    }

    public function utm()
    {
        return $this->morphOne('App\Utm', 'model');
    }

    public function routes()
    {
        return $this->belongsToMany(Route::class);
    }

    public function getUtmInfoAttribute()
    {
        return data_get($this, 'meta.request.utm');
    }

    public function TotalRefundedForGiftCard($id)
    {
        return optional($this->giftCardTransactions)->where('type', 'Refund')
            ->where('gift_card_id', $id)
            ->pluck('amount')
            ->sum();
        return collect($this->returns)->map(function ($return) use ($id) {
            return $return->TotalRefundedForGiftCard($id);
        })->sum();
    }

    public function payment()
    {
        return $this->morphTo();
    }

    public function recurring()
    {
        return $this->morphTo();
    }

    public function charge($amount)
    {
        return $this->payment->Charge($this, $amount);
    }

    public function authorizePayment($amount)
    {
        // need it for paypal
        $this->refresh();

        return $this->payment->Authorize($this, $amount);
    }

    public function capturePayment()
    {
        return optional($this->payment)->Capture($this);
    }

    public function cancelPayment()
    {
        return $this->payment->Cancel($this);
    }

    public function refundPayment($amount)
    {
        $this->capturePayment();
        return $this->payment->Refund($this, $amount);
    }

    public function getEmailAttribute()
    {
        $customer = $this->customer()->setEagerLoads([])->first();
        return $customer ? $customer->email : null;
    }

    public function scopeActive($query)
    {
        return $query->where('status', '!=', 'canceled');
    }

    public function scopeNotdeclined($query)
    {
        return $query->where('payment_status', '!=', 'declined');
    }

    public function getFrontEndAttribute()
    {
        return array_merge([
            'products' => $this->ProductsWithReturns(),
        ],
            $this->only([
                'id',
                'status',
                'discount',
                'payments',
                'shipping',
                'sub_total',
                'return_by',
                'created_at',
                'tax_amount',
                'grand_total',
                'discount_amount',
                'shipping_amount',
            ]));
    }

    public function ProductsWithReturns()
    {
        $returns = $this->returns;
        if ($returns->count() > 0) {
            $return_products = $returns
                ->map
                ->products
                ->flatten(1)
                ->groupBy(['id', 'type'])
                ->map->map(function ($item) {
                    $sum = $item->pluck('quantity')->sum();
                    return ['quantity' => $sum] + $item[0];
                })
                ->flatten(1);

            return collect($this->products)->map(function ($product) use ($return_products, $returns) {
                $exists = $return_products->filter(function ($item) use ($product) {
                    return $item['type'] == $product['type'] && $item['id'] == $product['id'];
                });

                if ($exists->count() > 0) {
                    $product['returned'] = [
                        'status' => data_get($exists->last(), 'status'),
                    ];
                    if ($exists->pluck('quantity')->sum() != $product['quantity']) {
                        $product['returned']['string'] = $exists->pluck('quantity')->sum(
                            ) . ' of ' . $product['quantity'];
                    } else {
                        $product['returned']['string'] = $product['quantity'];
                    }
                    $label = $returns->filter(function ($return) use ($product) {
                        return collect($return->products)->filter(function ($item) use ($product) {
                            return $item['id'] == $product['id'] && $item['type'] == $product['type'];
                        });
                    })->last();
                    if ($label) {
                        $product['returned']['label'] = data_get($label, 'shipping.tracking.label_pdf');
                    }
                }
                $returns_left = $product['quantity'] - $exists->pluck('quantity')->sum();
                if ($returns_left > 0 && $product['item_type'] != 'digital' && $product['item_type'] != 'service') {
                    $product['returns_left'] = $product['quantity'] - $exists->pluck('quantity')->sum();
                } else {
                    $product['returns_left'] = 0;
                }
                if ($product['item_type'] == 'digital' || $product['item_type'] == 'both') {
                    $product['download'] = '/api' . $product['path'] . '/download';
                }
                return $product;
            })->filter()->values();
        } else {
            return collect($this->products)->map(function ($product) {
                if ($product['item_type'] != 'digital' && $product['item_type'] != 'service') {
                    $product['returns_left'] = $product['quantity'];
                } else {
                    $product['returns_left'] = 0;
                }
                if ($product['item_type'] == 'digital' || $product['item_type'] == 'both') {
                    $product['download'] = '/api' . $product['path'] . '/download';
                }
                return $product;
            })->filter()->values();
        }
    }

    public function getTotalRefundAttribute()
    {
        return optional($this->payment)->refund
            +
            optional($this->giftCardTransactions)->where('type', 'Refund')
                ->pluck('amount')
                ->sum();
    }

    public function getRefundMaxAttribute()
    {
        return $this->grand_total - $this->totalRefund;
    }

    public function markAsDelivered()
    {
        $order = $this;
        if ($order->status == 'delivered' || $order->status == 'picked up') {
            return;
        }
        $order->update([
            'status' => $order->isPickup() ? 'picked up' : 'delivered',
            'shipping->tracking->arrived_at' => now(),
            'shipping->tracking->status' => 'Picked Up',
        ]);

        EmailsController::SendOrderDelivered($order);


        if (settings()->getValue('send_survey') == 'true') {
            dispatch(function () use ($order) {
                EmailsController::SendSurvey($order);
            })
                ->delay(now()->addHours(settings()->getValue('hours_after_delivery_to_send_survey')))
                ->onQueue('low');
        }
    }

    public function isPickup()
    {
        return !data_get($this, 'shipping.shippingType.delivery') && $this->shippable && !$this->subscription;
    }

    public function getShippesAttribute()
    {
        return data_get($this, 'shipping.shippingType.delivery') && $this->shippable;
    }

    public function getTotalPersonalizeAttribute()
    {
        return collect($this->products)->map(function ($product) {
            return data_get($product, 'personal.total');
        })->sum();
    }

    public function getTotalGiftOptionsAttribute()
    {
        return collect($this->products)->map(function ($product) {
            return data_get($product, 'gift_options.price');
        })->sum();
    }
}
