<?php

namespace App;

use App\Jobs\Miscellaneous;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Vendor extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    protected $fillable = [
        'name',
        'description',
        'is_visible'
    ];

    protected $with = [
        'media'
    ];

    protected $appends = [
        'path',
        'media_urls',
    ];

    protected $casts = [
        'is_visible' => 'boolean'
    ];

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function apply()
    {
        return $this->morphMany(CodeApply::class, 'applicability');
    }

    public function followers()
    {
        return $this->morphMany(Follow::class, 'model');
    }

    public function customers()
    {
        return $this->morphToMany(Customer::class, 'model', 'follows');
    }

    public function getPathAttribute()
    {
        return "/vendor/" . str_slug($this->name) . "/{$this->id}";
    }

    public function getMediaUrlsAttribute()
    {
        return $this->media->map(function ($item) {
            return $item->getUrl();
        });
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('main');
    }

    public function refreshCache($vendor)
    {
        $ids = $vendor->products()->isSearchable()->pluck('id');
        if ($ids->count() > 0) {
            settings()->setValue(
                'swiftype_update_ids',
                json_encode(
                    collect(
                        json_decode(settings()->getValue('swiftype_update_ids'))
                    )->merge($ids)->unique()->values()
                )
            );
        }
    }

    protected static function boot()
    {
        parent::boot();

        // static::saved(function ($vendor) {
        //     refreshCache();
        //     Miscellaneous::dispatch(\App\Vendor::class, 'refreshCache', $vendor);
        // });

        static::saved(function ($vendor) {
            if ($vendor->getOriginal('name') != $vendor->name) {
                $vendor->products->map->updateSearchFeild();
                Miscellaneous::dispatch(Vendor::class, 'refreshCache', $vendor);
            }
            refreshCache();
        });

        static::deleted(function ($vendor) {
            refreshCache();
        });
    }
}
