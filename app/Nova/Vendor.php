<?php

namespace App\Nova;

use Capitalc\Checkbox\Checkbox;
use Capitalc\Froala\Froala;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Illuminate\Support\Facades\DB;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\MorphToMany;
use <PERSON>vel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Maatwebsite\LaravelNovaExcel\Actions\DownloadExcel;

class Vendor extends Resource
{
    use ProductLister;

    public static string $model = \App\Vendor::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
        'description',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->rules('required', 'min:2'),

            Checkbox::make('Visible', 'is_visible'),

            Number::make('Products', '')
                ->resolveUsing(function () {
                    return DB::table('products')->select('vendor_id')->where('vendor_id', $this->id)->count();
                })->exceptOnForms(),


            Froala::make('Description')
                ->hideFromIndex(),

            Images::make('Images')
                ->conversionOnIndexView('thumb')
                ->conversionOnDetailView('thumb'),
            HasMany::make('Products'),
            MorphToMany::make('Followers', 'customers', '\App\Nova\Customer')
        ];
    }

    public function actions(NovaRequest $request): array
    {
        return [
            new DownloadExcel(),
        ];
    }
}
