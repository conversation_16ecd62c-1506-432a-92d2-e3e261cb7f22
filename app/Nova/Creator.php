<?php

namespace App\Nova;

use Capitalc\Checkbox\Checkbox;
use Capitalc\Froala\Froala;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Illuminate\Support\Facades\DB;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\MorphToMany;
use <PERSON>vel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Panel;

class Creator extends Resource
{
    use ProductLister;

    public static string $model = \App\Creator::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
        'description',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            Text::make('Name')
                ->rules('required', 'min:2'),
            Text::make('Hebrew Name', 'heb_name')
                ->hideFromIndex(),

            Number::make('Products', '')
                ->resolveUsing(function () {
                    return DB::table('creator_product')->select('creator_id')->where('creator_id', $this->id)->count();
                })->exceptOnForms(),

            Select::make('Type')
                ->options($this->options)
                ->rules('required'),

            Froala::make('Description')->hideFromIndex(),

            CheckBox::make('Search Query Redirect', 'redirect')
                ->help(
                    "When set as active, search queries that match " . $this->help_name(
                    ) . " will be redirected to the " . $this->help_name() . " page"
                ),

            new Panel('Images', [
                Images::make('Images')
                    ->conversionOnIndexView('thumb')
                    ->conversionOnDetailView('thumb'),
            ]),

            HasMany::make('Products'),
            MorphToMany::make('Followers', 'customers', '\App\Nova\Customer')
        ];
    }

    public function help_name(): string
    {
        return $this->name ? "'" . $this->name . "'" : 'creators name';
    }
}
