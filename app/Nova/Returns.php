<?php

namespace App\Nova;

use Capitalc\OrderCustomerView\OrderCustomerView;
use Capitalc\OrderDetailView\OrderDetailView;
use Capitalc\OrderProductsView\OrderProductsView;
use Capitalc\StaticText\StaticText;
use <PERSON>vel\Nova\Fields\Heading;
use Illuminate\Support\Str;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Currency;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Hidden;
use Laravel\Nova\Http\Requests\NovaRequest;
use NumberFormatter;

class Returns extends Resource
{
    public static string $model = \App\Returns::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];
    public static $searchRelations = [
        'order.customer' => ['name', 'email'],
        'order' => ['id']
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Status')
                ->onlyOnIndex()
                ->resolveUsing(function ($key) {
                    return Str::title($key);
                })
                ->fillUsing(function ($key) {
                    return strtolower($key);
                })
                ->sortable(),
            StaticText::make('Shipping Status', 'shipping_status'),

            DateTime::make('Created At')->onlyOnIndex()->displayUsing(function ($date) {
                return $date ? $date->format('m/d/Y h:i A') : null;
            })->sortable(),

            Text::make('Customer', 'customer_name')->onlyOnIndex(),

            BelongsTo::make('Order')->onlyOnIndex(),
            Currency::make('Refund Total', 'grand_total')->onlyOnIndex(),

            OrderDetailView::make('Order Date', 'id')->resolveUsing(function ($value) {
                return [
                    'date' => $this->order->created_at->format('F j, Y \a\t g:iA') . ' - Online Store.',
                    'order' => 'Order #' . $this->order->id
                ];
            })->onlyOnForms(),

            StaticText::make('Return Status', 'shipping')->resolveUsing(function ($value) {
                $carrier = $value['tracking']['service_code'] ?? null;
                $tracking_number = $value['tracking']['tracking_number'] ?? null;
                return $carrier . ' - ' . ($tracking_number ? ' Tracking ' . $tracking_number : '') . ' - ' . $this->shipping_status;
            })->onlyOnForms(),

            StaticText::make('Return By', 'dropoff')->onlyOnForms(),

            OrderProductsView::make('Products', 'products')
                ->resolveUsing(function ($value) {
                    $returns = $this->order->returns;
                    if ($returns->count() > 0) {
                        $return_products = $returns
                            ->where('id', '!=', $this->id)
                            ->map
                            ->products
                            ->flatten(1)
                            ->groupBy(['id', 'type'])
                            ->map->map(function ($item) {
                                $sum = $item->pluck('quantity')->sum();
                                return ['quantity' => $sum] + $item[0];
                            })
                            ->flatten(1);
                        return collect($this->products)
                            ->map(function ($product) use ($return_products, $returns) {
                                $exists = $return_products->filter(function ($item) use ($product) {
                                    return $item['type'] == $product['type'] && $item['id'] == $product['id'];
                                });
                                $returns_left = $product['quantity'] - $exists->pluck('quantity')->sum();
                                if ($returns_left > 0 && $product['item_type'] != 'digital') {
                                    $product['returns_left'] = $returns_left;
                                } else {
                                    $product['returns_left'] = 0;
                                }
                                return $product;
                            })->filter()->values();
                    } else {
                        return collect($this->products)->map(function ($product) {
                            if ($product['item_type'] != 'digital') {
                                $product['returns_left'] = $product['quantity'];
                            } else {
                                $product['returns_left'] = 0;
                            }
                            return $product;
                        })->filter()->values();
                    }
                })
                ->onlyOnForms(),

            Heading::make('Customer')->onlyOnForms(),

            OrderCustomerView::make('Contact', 'customer')->onlyOnForms(),

            StaticText::make('Account', 'order')->resolveUsing(function ($value) {
                if ($value->guest) {
                    return $this->customer->password ? 'Has Account - Checked out as guest' : '';
                }
            })->onlyOnForms(),

            StaticText::make('Group', 'id')->resolveUsing(function ($value) {
                return optional(\App\Group::find(optional($this->customer)->group_id))->name;
            })->onlyOnForms(),

            OrderDetailView::make('Loyalty Program', 'id')->resolveUsing(function ($value) {
                return ['...'];
            })->onlyOnForms(),

            OrderDetailView::make('Shipping', 'order')->resolveUsing(function ($value) {
                return [
                    'name' => data_get($value->shipping, 'shippingInfo.name'),
                    'address_line_1' => data_get($value->shipping, 'shippingInfo.address_line_1'),
                    'address_line_2' => data_get($value->shipping, 'shippingInfo.address_line_2') ?? '',
                    'city' => data_get($value->shipping, 'shippingInfo.city'),
                    'postal_code' => data_get($value->shipping, 'shippingInfo.postal_code'),
                    'state' => data_get($value->shipping, 'shippingInfo.state'),
                    'country' => data_get($value->shipping, 'shippingInfo.country')
                ];
            })->onlyOnForms(),

            OrderDetailView::make('Billing', 'order')->resolveUsing(function ($value) {
                if ($value->payments['creditInfo']) {
                    return [
                        'name' => data_get($value->payments, 'creditInfo.name'),
                        'address_line_1' => data_get($value->payments, 'creditInfo.address_line_1'),
                        'address_line_2' => data_get($value->payments, 'creditInfo.address_line_2') ?? '',
                        'city' => data_get($value->payments, 'creditInfo.city'),
                        'postal_code' => data_get($value->payments, 'creditInfo.postal_code'),
                        'state' => data_get($value->payments, 'creditInfo.state'),
                        'country' => data_get($value->payments, 'creditInfo.country')
                    ];
                }
            })->onlyOnForms(),

            OrderDetailView::make('History', 'customer')->resolveUsing(function ($value) {
                return [
                    (new NumberFormatter('en_US', NumberFormatter::ORDINAL))
                        ->format($value->returns->find(['id' => $this->id])->keys()->first())
                    . ' return',
                    'Customer since ' . $value->created_at->format('F j, Y ')
                ];
            })->onlyOnForms(),

            Heading::make('Device Details')->onlyOnForms(),

            StaticText::make('IP Address', 'order')->resolveUsing(function ($value) {
                return data_get($value, 'meta.request.ip');
            })->onlyOnForms(),

            // Text::make('Location', 'customer')->onlyOnForms(),

            StaticText::make('Operating System', 'order')->resolveUsing(function ($value) {
                return data_get($value, 'meta.request.system');
            })->onlyOnForms(),

            StaticText::make('Browser/Version', 'order')->resolveUsing(function ($value) {
                return data_get($value, 'meta.request.browser');
            })->onlyOnForms(),

            StaticText::make('Device', 'order')->resolveUsing(function ($value) {
                return data_get($value, 'meta.request.device');
            })->onlyOnForms(),

            Heading::make('Internal Notes')->onlyOnForms(),

        ];
    }
}
