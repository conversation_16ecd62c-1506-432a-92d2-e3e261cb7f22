<?php

namespace App\Nova;

use <PERSON><PERSON>zel\AjaxSelect\AjaxSelect;
use App\Nova\Filters\SubscriptionsByGroup;
use App\Nova\Filters\SubscriptionsByStatus;
use App\Nova\Metrics\ActiveSubscriptions;
use App\Nova\Metrics\CancelledSubscriptions;
use App\Nova\Metrics\LastSubscriptionPaymentDeclined;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\MorphMany;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\BelongsTo;
use Illuminate\Support\Str;
use Capitalc\RecurringInfo\RecurringInfo;
use Laravel\Nova\Http\Requests\NovaRequest;

class Subscription extends Resource
{
    use SuperAdmin;

    public static string $model = \App\Subscription::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];

    public static $searchRelations = [
        'customer' => ['name'],
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Customer')->withMeta([
                'extraAttributes' => [
                    'readonly' => true
                ]
            ]),

            BelongsTo::make('SubscriptionType'),

            BelongsTo::make('SubscriptionGroup'),

            Text::make('Last Payment')->onlyOnIndex(),

            Text::make('Status')
                ->exceptOnForms()
                ->resolveUsing(function ($key) {
                    return Str::title($key);
                })
                ->fillUsing(function ($key) {
                    return strtolower($key);
                })
                ->sortable(),
            Select::make('Status')->onlyOnForms()->options([
                'active' => 'Active',
                'canceled' => 'Canceled',
            ]),
            DateTime::make('Created At')->displayUsing(function ($date) {
                return $date ? $date->format('m/d/Y h:i A') : null;
            })->exceptOnForms(),
            RecurringInfo::make('Shipping', 'shipping')
                ->resolveUsing(function ($value) {
                    return [
                        'name' => data_get($value, 'name'),
                        'phone' => data_get($value, 'phone'),
                        'address_line_1' => data_get($value, 'address_line_1'),
                        'address_line_2' => data_get($value, 'address_line_2') ?? '',
                        'city' => data_get($value, 'city'),
                        'postal_code' => data_get($value, 'postal_code'),
                        'state' => data_get($value, 'state'),
                        'country' => data_get($value, 'country')
                    ];
                })
                ->hideFromIndex(),
            AjaxSelect::make('Payment', 'payment_id')
                ->get('/nova-custom-api/customer/{customer}/payments')
                ->parent('customer'),
            MorphMany::make('Orders'),
        ];
    }

    public function cards(NovaRequest $request): array
    {
        return [
            new ActiveSubscriptions,
            new CancelledSubscriptions,
            new LastSubscriptionPaymentDeclined
        ];
    }

    public function filters(NovaRequest $request): array
    {
        return [
            new SubscriptionsByStatus,
            new SubscriptionsByGroup,
        ];
    }

    public function actions(NovaRequest $request): array
    {
        return [
            new Actions\RecreateOrder
        ];
    }
}
