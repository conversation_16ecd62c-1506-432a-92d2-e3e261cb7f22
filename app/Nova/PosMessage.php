<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\MorphTo;
use <PERSON>vel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class PosMessage extends Resource
{
    use OrderFulfiller;

    public static string $model = \App\PosMessage::class;

    public static $title = 'id';

    public static $search = [
        'id',
        'model_id'
    ];

    public static function label()
    {
        return 'Pos Messages (' . \App\PosMessage::where('status', 'Unhandled')->count() . ')';
    }

    public static function authorizedToCreate(Request $request)
    {
        return false;
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }

    public function authorizedToUpdate(Request $request)
    {
        return false;
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            MorphTo::make('Model'),
            Text::make('Action')->sortable(),
            DateTime::make('Created At')->displayUsing(function ($date) {
                return $date ? $date->format('F j, Y') : null;
            })->sortable(),
            Text::make('Message'), //->onlyOnDetail(),
            Select::make('Status')->options([
                'Unhandled' => 'Unhandled',
                'Pending' => 'Pending',
                'Resolved' => 'Resolved',
            ])->sortable()
        ];
    }

    public function actions(NovaRequest $request): array
    {
        return [
            (new Actions\ChangeMessageStatus)->canRun(function () {
                return true;
            })
        ];
    }
}
