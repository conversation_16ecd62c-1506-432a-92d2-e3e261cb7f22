<?php

namespace App\Nova;

use <PERSON><PERSON><PERSON>\DependencyContainer\DependencyContainer;
use <PERSON><PERSON><PERSON>\DependencyContainer\HasDependencies;
use Capitalc\Checkbox\Checkbox;
use Capitalc\Creators\Creators;
use Capitalc\Currencyfield\Currencyfield;
use Capitalc\GeneralSearch\GeneralSearch;
use <PERSON>vel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\Heading;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class Discount extends Resource
{
    use HasDependencies, SuperAdmin;

    public static string $model = \App\Discount::class;

    public static $title = 'name';

    public static $search = [
        'name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Select::make('Discount Type', 'Automated')
                ->withMeta(['value' => $this->automated])
                ->options($this->boolean)->OnlyOnForms()
                ->rules('required'),

            Text::make('Discount', 'name')
                ->rules('max:255')
                ->OnlyOnIndex(),

            DependencyContainer::make([
                Text::make('Code', 'name')
                    ->rules('required', 'max:255')
                    ->creationRules('unique:discounts,name')
                    ->updateRules('unique:discounts,name,{{resourceId}}'),
            ])->dependsOn('Automated', 0),

            DependencyContainer::make([
                Text::make('Name')
                    ->rules('required', 'max:255')
                    ->creationRules('unique:discounts,name')
                    ->updateRules('unique:discounts,name,{{resourceId}}'),
            ])->dependsOn('Automated', 1),

            Heading::make('Details')->onlyOnForms(),
            Select::make('Type')
                ->withMeta(['value' => $this->type])
                ->onlyOnForms()
                ->options($this->options)
                ->rules('required'),

            Select::make('Country Availability', 'free_shipping_location')
                ->options([
                    'US' => 'United States',
                    'INT' => 'International',
                    'BOTH' => 'Both',
                ])
                ->onlyOnForms(),

            DependencyContainer::make([
                Currencyfield::make('Amount')
                    ->withMeta(['side' => 'right', 'symbol' => '%'])
            ])->dependsOn('type', 'percentage'),

            DependencyContainer::make([
                Currencyfield::make('Amount')
            ])->dependsOn('type', 'fixed'),

            DependencyContainer::make([
                Creators::make('Shipping Options', 'shipping_option_ids')
                    ->withMeta([
                        'extraAttributes' => [
                            'label' => 'Shipping Options',
                            'type' => '',
                            'availableResources' => \App\ShippingOption::get(['internal_name', 'id'])
                                ->map(function ($item) {
                                    return ['display' => $item['internal_name'], 'value' => $item['id']];
                                }),
                        ]
                    ]),

            ])->dependsOn('type', 'freeShipping'),

            Text::make('Amount')->resolveUsing(function ($amount) {
                if ($this->type == 'fixed') {
                    return '$' . number_format($amount, 2);
                } elseif ($this->type == 'percentage') {
                    return $amount . ' %';
                } else {
                    return 'Free Shipping';
                }
            })
                ->exceptOnForms(),

            Currencyfield::make('Minimuim Purchase', 'min')->hideFromIndex(),

            DependencyContainer::make([
                Currencyfield::make('Maximum Savings', 'max')
            ])->dependsOn('type', 'percentage'),

            Text::make('Applies To', function () {
                if ($this->model_type) {
                    return $this->models[$this->model_type];
                }
                return $this->model_type;
            })->onlyOnIndex(),

            DependencyContainer::make([
                Select::make('Appplies To', 'model_type')
                    ->options($this->models)->nullable()->OnlyOnForms(),

                DependencyContainer::make([
                    GeneralSearch::make('Products', 'product_model_ids')
                        ->withMeta([
                            'url' => 'only_products',
                            'label' => 'products'
                        ]),
                ])->dependsOn('model_type', 'products'),

                DependencyContainer::make([
                    GeneralSearch::make('Categories', 'category_model_ids')
                        ->withMeta([
                            'url' => 'categories',
                        ]),
                ])->dependsOn('model_type', 'categories'),

                DependencyContainer::make([
                    GeneralSearch::make('Creators', 'creator_model_ids')
                        ->withMeta([
                            'url' => 'creators',
                        ]),
                ])->dependsOn('model_type', 'creators'),

                DependencyContainer::make([
                    GeneralSearch::make('Vendors', 'vendor_model_ids')
                        ->withMeta([
                            'url' => 'vendors',
                        ]),
                ])->dependsOn('model_type', 'vendors'),
            ])->dependsOn('type', 'percentage'),

            DependencyContainer::make([
                Select::make('Appplies To', 'model_type')
                    ->options($this->models)->nullable()->OnlyOnForms(),

                DependencyContainer::make([
                    GeneralSearch::make('Products', 'product_model_ids')
                        ->withMeta([
                            'url' => 'only_products',
                            'label' => 'products'
                        ]),
                ])->dependsOn('model_type', 'products'),

                DependencyContainer::make([
                    GeneralSearch::make('Categories', 'category_model_ids')
                        ->withMeta([
                            'url' => 'categories',
                        ]),
                ])->dependsOn('model_type', 'categories'),

                DependencyContainer::make([
                    GeneralSearch::make('Creators', 'creator_model_ids')
                        ->withMeta([
                            'url' => 'creators',
                        ]),
                ])->dependsOn('model_type', 'creators'),

                DependencyContainer::make([
                    GeneralSearch::make('Vendors', 'vendor_model_ids')
                        ->withMeta([
                            'url' => 'vendors',
                        ]),
                ])->dependsOn('model_type', 'vendors'),

            ])->dependsOn('type', 'fixed'),

            Heading::make('Eligibility')->onlyOnForms(),

            Select::make('Eligibility', 'eligibility_type')
                ->options([
                    '' => 'All Customers',
                    'App\\Customer' => 'Customers',
                    'App\\Group' => 'Groups',
                ])->onlyOnForms(),

            DependencyContainer::make([
                GeneralSearch::make('Customers', 'customer_eligibility_id')
                    ->withMeta([
                        'url' => 'customers',
                        'label' => 'customers'
                    ]),
            ])->dependsOn('eligibility_type', 'App\\Customer'),
            DependencyContainer::make([
                GeneralSearch::make('Groups', 'group_eligibility_id')
                    ->withMeta([
                        'url' => 'groups',
                        'label' => 'groups'
                    ]),
            ])->dependsOn('eligibility_type', 'App\\Group'),

            Checkbox::make('Eligble for Gift Cards', 'apply_to_gc')
                ->onlyOnForms()
                ->help('Should apply for EGift Cards and Phisycal Gft Cards'),

            Checkbox::make('Eligble for Sale Items', 'apply_to_sale')
                ->onlyOnForms()->withMeta([
                    'value' => $this->apply_to_sale ?? true
                ]),

            Heading::make('Usage Limits')->onlyOnForms(),
            Checkbox::make('Limitable')->onlyOnForms(),

            DependencyContainer::make([
                Number::make('Limit amount of uses', 'limit')
                    ->OnlyOnForms()->nullable()->help('Excludes cancelled orders'),

                Number::make('Limit per customer', 'limit_customer')
                    ->OnlyOnForms()->nullable()->help('This will require the user to log in'),

            ])->dependsOn('limitable', true),

            Number::make('Limit')
                ->exceptOnForms()
                ->rules('nullable'),

            Heading::make('Schedule')->onlyOnForms(),
            DateTime::make('Start')
                ->onlyOnForms()->nullable(),

            DateTime::make('End')
                ->onlyOnForms()->nullable(),
        ];
    }
}
