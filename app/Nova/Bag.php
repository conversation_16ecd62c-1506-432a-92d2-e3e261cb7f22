<?php

namespace App\Nova;

use App\Customer;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Bag extends Resource
{
    use ProductLister;

    public static string $model = \App\Bag::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];

    public static array $searchRelations = [
        'customer' => ['name', 'email'],
    ];

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->groupBy('customer_id');
    }

    public function fields(NovaRequest $request): array
    {
        return [
            BelongsTo::make('Customer'),
            Text::make('Email', 'customer_id')->resolveUsing(function ($customer_id) {
                return Customer::find($customer_id)->email;
            }),
            Date::make('First Product Added', 'created_at')->resolveUsing(function () {
                return Customer::find($this->customer_id)->bag->sortBy('created_at')->map(function ($bag) {
                    return $bag->created_at;
                })->first();
            })->displayUsing(function ($date) {
                return $date ?$date->format('m/d/Y h:i A') : null;
            }),

            Date::make('Last Modified', 'updated_at')->resolveUsing(function () {
                return Customer::find($this->customer_id)->bag->sortByDesc('updated_at')->map(function ($bag) {
                    return $bag->created_at;
                })->first();
            })->displayUsing(function ($date) {
                return $date ? $date->format('m/d/Y h:i A') : null;
            }),
            Boolean::make('Abandoned', 'customer_id')->resolveUsing(function () {
                return Customer::find($this->customer_id)->abandoned();
            }),

            Code::make('Items In Cart', 'id')
                ->json()
                ->resolveUsing(function () {
                    return json_encode(Customer::find($this->customer_id)->bag->where('later', false)->map(function ($bag) {
                        return $bag->model->getFrontEndAttribute($bag->quantity);
                    }));
                })
                ->onlyOnDetail(),

            Code::make('Items Saved For Later', 'id')
                ->json()
                ->resolveUsing(function () {
                    return json_encode(Customer::find($this->customer_id)->bag->where('later', true)->map(function ($bag) {
                        return $bag->model->getFrontEndAttribute($bag->quantity);
                    }));
                })
                ->onlyOnDetail(),
        ];
    }
}
