<?php

namespace App\Nova;

use Ebess\AdvancedNovaMediaLibrary\Fields\Files;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use NovaButton\Button;

class Export extends Resource
{
    use OrderFulfiller;

    public static string $model = \App\Export::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name'
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            Text::make('Name'),
            DateTime::make('Created At')->displayUsing(function ($date) {
                return $date ? $date->format('m/d/Y h:i A') : null;
            })->exceptOnForms(),
            Files::make('File')->onlyOnDetail(),
            Button::make('Go to creating a Report')->onlyOnDetail()->link('/admin/report', '_self')->successText(' ')
        ];
    }
}
