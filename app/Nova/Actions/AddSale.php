<?php

namespace App\Nova\Actions;

use <PERSON><PERSON>zel\DependencyContainer\DependencyContainer;
use App\Product;
use Capitalc\Checkbox\Checkbox;
use Capitalc\Currencyfield\Currencyfield;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\Heading;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class AddSale extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            if (!$fields->add_sale) {
                $model->sale()->delete();
            } else {
                $model->sale()->updateOrCreate(['model_type' => 'App\Product', 'model_id' => $model->id], [
                    'type' => $fields->sale_type,
                    'amount' => $fields->sale_amount,
                    'from' => $fields->sale_from,
                    'start' => $fields->start_sale,
                    'end' => $fields->end_sale,
                ]);
            }
            foreach ($model->variationInfos as $variationInfo) {
                if (!$fields->add_sale) {
                    $variationInfo->sale()->delete();
                } else {
                    $variationInfo->sale()->updateOrCreate(
                        ['model_type' => 'App\VariationInfo', 'model_id' => $variationInfo->id],
                        [
                            'type' => $fields->sale_type,
                            'amount' => $fields->sale_amount,
                            'from' => $fields->sale_from,
                            'start' => $fields->start_sale,
                            'end' => $fields->end_sale,
                        ]
                    );
                }
            }
            Cache::forget("product_{$model->id}");
        }

        $updateIds = Product::setEagerLoads([])->isSearchable()->whereIn('id', $models->pluck('id'))->get(['id']
        )->map->id;

        Product::find($updateIds)->each->updateSearchFeild();

        $ids = collect(json_decode(settings()->getValue('swiftype_update_ids')));
        settings()->setValue('swiftype_update_ids', json_encode($ids->push($updateIds)->flatten()->values()->unique()));
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Heading::make(
                'Sale price will apply to all selected products and all its variations. This action will override all online sale pricing on all selected products and variations.'
            ),

            Checkbox::make('Online Sale Price', 'add_sale')
                ->hideFromIndex()
                ->withMeta(['value' => true]),
            DependencyContainer::make([
                Select::make('Sale Type')->options((new Product)->saleOptions),

                DependencyContainer::make([
                    Currencyfield::make('Percent', 'sale_amount')
                        ->withMeta(['side' => 'right', 'symbol' => '%']),
                    Select::make('Deduct From', 'sale_from')->options([
                        'list_price' => 'List Price',
                        'store_price' => 'Store Price',
                        'online_price' => 'Online Price',
                        'cost_price' => 'Cost Price',
                        'sale_price' => 'Sale Price',
                    ]),
                ])->dependsOn('sale_type', 'percent'),

                DependencyContainer::make([
                    Currencyfield::make('Sale Price', 'sale_amount')
                ])->dependsOn('sale_type', 'fixed'),

                Date::make('Start Sale'),
                Date::make('End Sale'),
            ])->dependsOn('add_sale', true)->hideFromIndex(),
        ];
    }
}
