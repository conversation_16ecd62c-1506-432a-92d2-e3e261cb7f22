<?php

namespace App\Nova;

use App\Http\Controllers\Api\OrdersController;
use App\Nova\Filters\OrderPaymentStatus;
use App\Nova\Filters\OrderStatus;
use App\Nova\Metrics\AverageOrderTotal;
use App\Nova\Metrics\Orders;
use App\Nova\Metrics\OrdersByState;
use App\Nova\Metrics\OrdersPerDay;
use Capitalc\OrderActionsView\OrderActionsView;
use Capitalc\OrderCustomerView\OrderCustomerView;
use Capitalc\OrderDetailView\OrderDetailView;
use Capitalc\OrderPosView\OrderPosView;
use Capitalc\OrderPricingView\OrderPricingView;
use Capitalc\OrderProductsView\OrderProductsView;
use Capitalc\StaticText\StaticText;
use Laravel\Nova\Fields\Heading;
use Laravel\Nova\Fields\Hidden;
use Illuminate\Support\Str;
use Laravel\Nova\Fields\Currency;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use NumberFormatter;

class Order extends Resource
{
    public static string $model = \App\Order::class;

    public static $title = 'id';

    public static $globalSearchLink = 'edit';

    public static $search = [
        'id',
    ];

    public static $searchRelations = [
        'customer' => ['name'],
    ];


    public function fields(NovaRequest $request): array
    {
        return $this->allFields();
    }

    public function allFields(): array
    {
        if (request()->is('nova-api/orders')) {
            return $this->outsideFields();
        }
        return $this->insideFields();
    }

    public function outsideFields(): array
    {
        return [

            ID::make()
                ->sortable(),
            Text::make('Customer')
                ->resolveUsing(function ($customer) {
                    return $customer->name ?? '';
                })
                ->onlyOnIndex(),
            Currency::make('Grand Total')->onlyOnIndex(),

            Text::make('Status')
                ->onlyOnIndex()
                ->resolveUsing(function ($key) {
                    if ($key == 'paid' && $this->shippable) {
                        return 'Unfulfilled';
                    }
                    return Str::title($key);
                })
                ->fillUsing(function ($key) {
                    if ($key == 'Unfulfilled') {
                        return 'paid';
                    }
                    return strtolower($key);
                })
                ->sortable(),
            Text::make('Payment Status')
                ->onlyOnIndex()
                ->resolveUsing(function ($key) {
                    return Str::title($key);
                })
                ->fillUsing(function ($key) {
                    return strtolower($key);
                })
                ->sortable(),

            DateTime::make('Created At')->displayUsing(function ($date) {
                return $date ? $date->format('m/d/Y h:i A') : null;
            })->sortable(),

        ];
    }

    public function insideFields(): array
    {
        return array_merge(
            [
                OrderActionsView::make('Actions', 'id')
                    ->withMeta([
                        'order' => $this->load('customer')->toArray(),
                    ])
                    ->onlyOnForms(),
                ID::make()
                    ->sortable(),

                Hidden::make('order', 'id')->resolveUsing(function ($value) {
                    return $this->toArray();
                })->onlyOnForms(),

                StaticText::make('Order Date', 'created_at')->resolveUsing(function ($value) {
                    return $value->format('F j, Y \a\t g:iA') . ' - Online Store.';
                })->onlyOnForms(),
                StaticText::make('Order Status', 'shipping')->resolveUsing(function ($value) {
                    if (!!$this->routes()->count()) {
                        return
                            'This is from route: ' . '<a href="/admin/resources/routes/' . $this->routes->first(
                            )->id . '" class="no-underline dim text-primary font-bold">' . $this->routes->first(
                            )->route_id . '</a>'
                        ;
                    }
                    $carrier = $value['tracking']['carrier'] ?? null;
                    $tracking_number = $value['tracking']['tracking_number'] ?? null;

                    return $carrier . ' - ' . ($tracking_number ? ' Tracking ' . $tracking_number : '');
                })->onlyOnForms(),

                OrderProductsView::make('Products', 'products')
                    ->resolveUsing(function () {
                        return (new OrdersController)->returnProducts($this);
                    })
                    ->onlyOnForms(),

                OrderDetailView::make('Shipping Method', 'shipping')->resolveUsing(function ($value) {
                    $kvp = [
                        data_get($value, 'shippingType.name') => '$' . number_format(
                                data_get($value, 'shippingType.price'),
                                2
                            )
                    ];
                    return ['kvp' => $kvp];
                })->onlyOnForms(),
                StaticText::make('Arrive By', 'created_at')->resolveUsing(function () {
                    $date = data_get($this, 'shipping.shippingType.estimated_arrival');
                    return $date ? now()->parse($date)->format('F j, Y \a\t g:iA') : '';
                })->onlyOnForms(),

                OrderPricingView::make('Pricing', 'id')->resolveUsing(function ($value) {
                    $kvp = [
                        'Subtotal' => $this->sub_total,
                        'Delivery Fee' => $this->shipping_amount,
                        'Tax' => $this->tax_amount,
                        'Discount' => null,
                        'Gift options Fee' => 0.00,
                        'Personalizations Fee' => 0.00,
                        'Order Total' => $this->grand_total,
                        'Paid with' => $this->paid_with(),
                        'Total Refunded' => data_get($this, 'payments.refunds_total'),
                        'Total E-Gift Card Refunded' => data_get($this, 'payments.egift_refunds_total'),
                        'Costed To Ship' => data_get($this, 'shipping.tracking.shipmentCost')
                    ];
                    $this->discount_amount ? $kvp['Discount'] = ['discount' => $this->discount] : null;

                    $kvp['Gift options Fee'] = collect($this->products)->sum('gift_options.price');

                    $kvp['Personalizations Fee'] = collect($this->products)->sum('personal.total');

                    return $kvp;
                })->onlyOnForms(),
            ],
            $this->recurringText(),
            [


                OrderPosView::make('Pos Store Message', 'meta')->resolveUsing(function ($value) {
                    return data_get($value, 'pos');
                })->withMeta([
                    'online' => false,
                    'cancelled' => $this->status == 'cancelled'
                ])->onlyOnForms(),
                OrderPosView::make('Pos Online Message', 'meta')->resolveUsing(function ($value) {
                    return data_get($value, 'pos_online');
                })->withMeta([
                    'online' => true,
                    'cancelled' => $this->status == 'cancelled'
                ])->onlyOnForms(),
            ],
            $this->cancelledPosMessage(),
            [
                Heading::make('Customer')->onlyOnForms(),

                OrderCustomerView::make('Contact', 'customer')->onlyOnForms(),

                StaticText::make('Account', 'id')->resolveUsing(function ($value) {
                    if ($this->guest) {
                        return $this->customer->password ? 'Has Account - Checked out as guest' : false;
                    } else {
                        return true;
                    }
                })->onlyOnForms(),

                StaticText::make('Group', 'id')->resolveUsing(function ($value) {
                    return optional(\App\Group::find(optional($this->customer)->group_id))->name;
                })->onlyOnForms(),

                StaticText::make('Loyalty Program', 'id')->resolveUsing(function ($value) {
                    return '...';
                })->onlyOnForms(),

            ],
            $this->shippingText(),
            $this->pickUpText(),
            [
                OrderDetailView::make('Billing', 'payments')->resolveUsing(function ($value) {
                    if ($value['creditInfo']) {
                        return [
                            'name' => data_get($value, 'creditInfo.name'),
                            'address_line_1' => data_get($value, 'creditInfo.address_line_1'),
                            'address_line_2' => data_get($value, 'creditInfo.address_line_2') ?? '',
                            'city' => data_get($value, 'creditInfo.city'),
                            'postal_code' => data_get($value, 'creditInfo.postal_code'),
                            'state' => data_get($value, 'creditInfo.state'),
                            'country' => data_get($value, 'creditInfo.country')
                        ];
                    }
                })->onlyOnForms(),

                OrderDetailView::make('History', 'customer')->resolveUsing(function ($value) {
                    return [
                        (new NumberFormatter('en_US', NumberFormatter::ORDINAL))
                            ->format($value->orders->find(['id' => $this->id])->keys()->first() + 1)
                        . ' order',
                        'Customer since ' . $value->created_at->format('F j, Y ')
                    ];
                })->onlyOnForms(),

                Heading::make('Device Details')->onlyOnForms(),

                StaticText::make('IP Address', 'meta')->resolveUsing(function ($value) {
                    return data_get($value, 'request.ip');
                })->onlyOnForms(),

                // Text::make('Location', 'customer')->onlyOnForms(),

                StaticText::make('Operating System', 'meta')->resolveUsing(function ($value) {
                    return data_get($value, 'request.system');
                })->onlyOnForms(),

                StaticText::make('Browser/Version', 'meta')->resolveUsing(function ($value) {
                    return data_get($value, 'request.browser');
                })->onlyOnForms(),

                StaticText::make('Device', 'meta')->resolveUsing(function ($value) {
                    return data_get($value, 'request.device');
                })->onlyOnForms(),

                Heading::make('Utm Details')->onlyOnForms(),

                StaticText::make('Source', 'utmInfo')->resolveUsing(function ($value) {
                    return data_get($value, 'utm_source');
                })->onlyOnForms(),

                // Text::make('Location', 'customer')->onlyOnForms(),

                StaticText::make('Medium', 'utmInfo')->resolveUsing(function ($value) {
                    return data_get($value, 'utm_medium');
                })->onlyOnForms(),

                StaticText::make('Campaign', 'utmInfo')->resolveUsing(function ($value) {
                    return data_get($value, 'utm_campaign');
                })->onlyOnForms(),

                StaticText::make('Term', 'utmInfo')->resolveUsing(function ($value) {
                    return data_get($value, 'utm_term');
                })->onlyOnForms(),

                StaticText::make('Content', 'utmInfo')->resolveUsing(function ($value) {
                    return data_get($value, 'utm_content');
                })->onlyOnForms(),


                Heading::make('Internal Notes')->onlyOnForms(),


            ]
        );
    }

    public function paid_with()
    {
        $payment_method = collect();
        if (data_get($this->payments, 'creditInfo.payment_type') == 'creditCard') {
            $payment_method->push([
                'method' => ucfirst(
                    data_get($this->payments, 'creditInfo.type') . ' ... ' . data_get(
                        $this->payments,
                        'creditInfo.last_four'
                    )
                ),
                'link' => '/admin/resources/credit-card-payments/' . $this->payment_id
            ]);
        }
        if (data_get($this->payments, 'creditInfo.payment_type') == 'payPal') {
            $payment_method->push([
                'method' => 'Paypal',
                'link' => '/admin/resources/paypal-payments/' . $this->payment_id
            ]);
        }
        if ($this->payments['giftCard']) {
            collect($this->payments['giftCard'])->each(function ($card) use (&$payment_method) {
                $payment_method->push([
                    'method' => 'Gift card ending in ' . data_get($card, 'code'),
                    'link' => '/admin/resources/gift-cards/' . data_get($card, 'id')
                ]);
            });
        }
        return $payment_method;
    }

    public function recurringText()
    {
        if (empty($this->recurring_type) || empty($this->recurring_id)) {
            return [];
        }
        return [
            Text::make('Recurring')->resolveUsing(function () {
                if ($this->recurring) {
                    if ($this->recurring_type == 'App\\Subscription') {
                        return collect('Subscription, ID: ' . $this->recurring_id);
                    } else {
                        return collect('Recurring, ID: ' . $this->recurring_id);
                    }
                }
                return collect('');
            })->onlyOnForms(),
        ];
    }

    public function cancelledPosMessage()
    {
        if (data_get($this, 'meta.pos_cancelled') || data_get($this, 'meta.pos_online_cancelled')) {
            return [
                Text::make('Pos Cancelled Store Order Message', 'meta')->resolveUsing(function ($value) {
                    return data_get($value, 'pos_cancelled');
                })->onlyOnForms(),
                Text::make('Pos Cancelled Online Order Message', 'meta')->resolveUsing(function ($value) {
                    return data_get($value, 'pos_online_cancelled');
                })->onlyOnForms(),
            ];
        }
        return [];
    }

    public function shippingText()
    {
        if (data_get($this, 'shipping.shippingInfo') && $this->shippes) {
            return [
                OrderDetailView::make('Shipping', 'shipping')->resolveUsing(function ($value) {
                    return [
                        'name' => data_get($value, 'shippingInfo.name'),
                        'phone' => data_get($value, 'shippingInfo.phone'),
                        'address_line_1' => data_get($value, 'shippingInfo.address_line_1'),
                        'address_line_2' => data_get($value, 'shippingInfo.address_line_2') ?? '',
                        'city' => data_get($value, 'shippingInfo.city'),
                        'postal_code' => data_get($value, 'shippingInfo.postal_code'),
                        'state' => data_get($value, 'shippingInfo.state'),
                        'country' => data_get($value, 'shippingInfo.country')
                    ];
                })->onlyOnForms(),
            ];
        } else {
            return [];
        }
    }

    public function pickUpText()
    {
        if (data_get($this, 'shipping.pickupInfo')) {
            return [
                OrderDetailView::make('Pick Up Info', 'shipping')->resolveUsing(function ($value) {
                    return [
                        'name' => data_get($value, 'pickupInfo.name'),
                        'email' => data_get($value, 'pickupInfo.email'),
                        'phone' => data_get($value, 'pickupInfo.phone'),
                    ];
                })->onlyOnForms(),
            ];
        } else {
            return [];
        }
    }

    public function cards(NovaRequest $request): array
    {
        return [
            new OrdersByState,
            new OrdersPerDay,
            new Orders,
            new AverageOrderTotal,
        ];
    }


    public function filters(NovaRequest $request): array
    {
        return [
            new OrderStatus,
            new OrderPaymentStatus,
        ];
    }

    public function actions(NovaRequest $request): array
    {
        return [
            (new Actions\MarkAsShipped)
                ->exceptOnIndex()
                ->canSee(function () {
                    if ($this->id) {
                        return $this->status != 'shipped'
                            && $this->status != 'cancelled'
                            && $this->status != 'delivered'
                            && $this->status != 'unpaid'
                            && $this->shippable
                            && !$this->isPickup();
                    }
                    return true;
                })
                ->confirmText('Are you sure you want to mark this order as shipped?')
                ->confirmButtonText('Mark As Shipped'),

            (new Actions\MarkAsDelivered)
                ->exceptOnIndex()
                ->canSee(function () {
                    if ($this->id) {
                        return $this->status != 'delivered'
                            && $this->status != 'cancelled'
                            && $this->status != 'unpaid'
                            && $this->shippable
                            && !$this->isPickup();
                    }
                    return true;
                })
                ->confirmText('Are you sure you want to mark this order as delivered?')
                ->confirmButtonText('Mark As Delivered'),
            (new Actions\MarkAsReady)
                ->exceptOnIndex()
                ->canSee(function () {
                    if ($this->id) {
                        return $this->status != 'ready'
                            && $this->status != 'cancelled'
                            && $this->status != 'picked up'
                            && $this->status != 'unpaid'
                            && $this->shippable
                            && $this->isPickup()
                            && !$this->recurring_id;
                    }
                    return true;
                })
                ->confirmText('Are you sure you want to mark this order as ready for pick up?')
                ->confirmButtonText('Mark As Ready For Pick Up'),

            (new Actions\MarkAsPickedUp)
                ->exceptOnIndex()
                ->canSee(function () {
                    if ($this->id) {
                        return $this->status != 'picked up'
                            && $this->status != 'cancelled'
                            && $this->status != 'unpaid'
                            && $this->shippable
                            && $this->isPickup()
                            && !$this->recurring_id;
                    }
                    return true;
                })
                ->confirmText('Are you sure you want to mark this order as picked up?')
                ->confirmButtonText('Mark As Picked Up'),
        ];
    }
}
