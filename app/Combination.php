<?php

namespace App;

use Bugsnag;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;

class Combination extends Model
{
    public $guarded = [];

    public $casts = [
        'next_products' => 'array',
        'upcoming_products' => 'array',
        'upcoming_date' => 'date',
        'next_date' => 'date',
    ];

    public function type()
    {
        return $this->belongsTo(SubscriptionType::class);
    }

    public function group()
    {
        return $this->belongsTo(SubscriptionGroup::class);
    }

    public function getSubscriptionsAttribute()
    {
        return Subscription::where('subscription_group_id', $this->group_id)
            ->where('subscription_type_id', $this->type_id)
            ->get();
    }

    public function setUpcoming()
    {
        if (!$this->filter_name) {
            $this->update([
                'filter_name' => $this->group->filter->name
            ]);
        }

        //always update
        $prices = $this->group->items->map->model->pluck('price');

        $this->update([
            'from_price' => $prices->min(),
            'to_price' => $prices->max(),
        ]);
        $upcomingDate = $this->upcomingDate();

        $this->update([
            'upcoming_date' => data_get($upcomingDate, 'date'),
            'upcoming_filter_item_id' => data_get($upcomingDate, 'id'),
        ]);

        $this->update([
            'upcoming_products' => $this->upcomingProducts(),
        ]);
        $this->update([
            'upcoming_string' => $this->upcomingString(),
        ]);
    }

    public function upcomingProducts()
    {
        $items = $this->group->items;

        $ids = DB::table('filter_item_product')
            ->where('filter_item_id', $this->upcoming_filter_item_id)
            ->whereIn('product_id', $items->map->product_id)
            ->get()
            ->pluck('product_id');

        return $this->group->items->whereIn('product_id', $ids)->map(function ($item) {
            return $item->model->front_end;
        })->filter()->values();
    }

    public function upcomingDate()
    {
        return $this
            ->type
            ->getDatesAttribute()
            ->where('date', '>=', today())
            ->sortBy('date')
            ->first();
    }

    public function upcomingString()
    {
        $products = collect($this->upcoming_products);

        $filter_item = DB::table('filter_items')->find($this->upcoming_filter_item_id);

        $filter_item_name = $filter_item ? $filter_item->name : '';

        $string = $this->filter_name . ' ' . $filter_item_name;

        if ($products->pluck('type')->contains('variation')) {
            $key = collect(
                json_decode($products->where('type', 'variation')->first()['meta'])
            )->keys()->first();

            $count = $products->count();
            $plural = Str::plural(trim($key), $count);

            $string = "{$count} {$plural} of {$string}";
        }

        return $string;
    }

    public function nextProducts()
    {
        # code...
    }

    public function nextDate()
    {
        # code...
    }

    public function nextString()
    {
        # code...
    }

    public static function createCombinations()
    {
        $uniques = Subscription::get()->unique(function ($item) {
            return $item->subscription_group_id . '_' . $item->subscription_type_id;
        });

        $uniques->each(function ($item) {
            $combination = self::firstOrCreate([
                'group_id' => $item->subscription_group_id,
                'type_id' => $item->subscription_type_id,
            ]);
            if ($combination->wasRecentlyCreated) {
                $combination->setUpcoming();
            }
        });
    }

    public static function groupSaved($group)
    {
        self::allCombinations();
        Combination::where('group_id', $group->id)->each(function ($combination) {
            $id = $combination->id;
            dispatch(function () use ($id) {
                Combination::find($id)->setUpcoming();
            });
        });
    }

    public static function typeSaved($type)
    {
        self::allCombinations();
        Combination::where('type_id', $type->id)->each(function ($combination) {
            $id = $combination->id;
            dispatch(function () use ($id) {
                Combination::find($id)->setUpcoming();
            });
        });
    }

    public static function allCombinations()
    {
        $type_ids = SubscriptionType::pluck('id')->map(function ($item) {
            return ['type_id' => $item];
        });

        $group_ids = SubscriptionGroup::pluck('id')->map(function ($item) {
            return ['group_id' => $item];
        });

        $joins = $type_ids->crossJoin($group_ids)->map(function ($item) {
            return collect($item)->mapWithKeys(function ($item) {
                return $item;
            });
        });


        $combinations = Combination::all();

        $joins->each(function ($join) use ($combinations) {
            $exists = $combinations
                ->where('type_id', $join['type_id'])
                ->where('group_id', $join['group_id'])
                ->count();
            if (!$exists) {
                dispatch(function () use ($join) {
                    $combination = Combination::create([
                        'type_id' => $join['type_id'],
                        'group_id' => $join['group_id'],
                    ]);
                    $combination->setUpcoming();
                });
            }
        });
        return true;
    }

    public static function sendEmails($date = null)
    {
        try {
            $date = $date ?? today()->addWeek(3);

            $subscription_ids = self::whereDate('upcoming_date', $date)
                ->get()
                ->map
                ->subscriptions
                ->flatten()
                ->where('status', '!=', 'canceled')
                ->values()
                ->map
                ->id
                ->chunk(15);


            $subscription_ids->each(function ($ids) {
                dispatch(function () use ($ids) {
                    Subscription::find($ids)->each->sendEmail();
                });
            });
        } catch (Exception $ex) {
            Bugsnag::notifyException($ex);
        }
    }

    public static function createOrders($date = null)
    {
        try {
            $date = $date ?? today()->addWeeks(2);

            $subscription_ids = self::whereDate('upcoming_date', $date)
                ->get()
                ->map
                ->subscriptions
                ->flatten()
                ->where('status', '!=', 'canceled')
                ->values()
                ->map
                ->id;

            $date = $date ? now()->parse($date)->toDateString() : null;

            $subscription_ids->each(function ($id) use ($date) {
                dispatch(function () use ($id, $date) {
                    $date = $date ? now()->parse($date) : null;
                    Subscription::find($id)->createOrder($date);
                });
            });
        } catch (Exception $ex) {
            Bugsnag::notifyException($ex);
        }
    }
}
