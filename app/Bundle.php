<?php

namespace App;

use <PERSON>tie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use <PERSON>tie\MediaLibrary\InteractsWithMedia;
use Outl1ne\MenuBuilder\Models\MenuItem;

class Bundle extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    protected $with = [
        'media'
    ];

    protected $appends = [
        'path',
        'categories_ids',
        'filter_ids',
        'fake_price',
        'show_hebrew',
        'active',
        'breadcrumbs',
        'media_urls',
        'front_end_products',
        'weight',
        'max'
    ];

    public function lists()
    {
        return $this->belongsTo(Lists::class);
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class);
    }

    public function bundleItems()
    {
        return $this->hasMany(BundleItem::class);
    }

    public function getMaxAttribute()
    {
        return max(
            $this->bundleItems->map(function ($item) {
                return $item->model->max / $item->quantity;
            })->min(),
            0
        );
    }

    public function getMediaUrlsAttribute()
    {
        $data = $this->media->sortBy('order_column')
            ->map(function ($item) {
                if (!$item) {
                    return null;
                }
                if ($item->collection_name == 'media') {
                    return [
                        'grid' => $item->getUrl('grid'),
                        'large' => $item->getUrl('large'),
                        'lightbox' => $item->getUrl('lightbox'),
                        'thumbnail' => $item->getUrl('thumbnail'),
                    ];
                }
            })->filter()->values();

        if ($this->show_images) {
            $this->bundleItems->each(function ($item) use (&$data) {
                if (!$item->model) {
                    return;
                }
                foreach ($item->model->getMediaUrlsAttribute() as $value) {
                    $data->add($value);
                }
            })->filter();
        }
        return $data;
    }

    public function getShowHebrewAttribute()
    {
        if ($this->heb_title || $this->heb_description || $this->heb_short_desc) {
            return true;
        }
        return false;
    }

    public function setShowHebrewAttribute()
    {
        return false;
    }

    public function getPrice()
    {
        return $this->price;
    }

    public function getFormattedProductsAttribute()
    {
        return $this->bundleItems->map(function ($item) {
            if (!$model = $item->model) {
                return;
            }
            $data = $model->getFrontEndAttribute();
            $return = [
                'id' => $model->id,
                'type' => $data['type'],
                'title' => $model->title,
                'price' => $model->toArray()['price'] * $item->quantity,
                'media' => $model->getFirstMediaUrl('media', 'grid'),
                'sku' => $model->sku,
                'quantity' => $item->quantity,
            ];
            if ($data['type'] == 'variation') {
                $return['meta'] = json_decode($model->meta);
                $return['title'] = $model->product->title;
                $return['media'] = $model->product->getFirstMediaUrl('media', 'grid');
            }
            return $return;
        })->filter();
    }

    public function getFrontEndProductsAttribute()
    {
        return $this->bundleItems->map(function ($bundleItem) {
            if (!$bundleItem->model) {
                return;
            }
            $data = $bundleItem->model->getFrontEndAttribute() + [
                    'short_desc' => $bundleItem->model->short_desc,
                    'heb_title' => $bundleItem->model->heb_title,
                    'heb_short_desc' => $bundleItem->model->heb_short_desc,
                ];
            if ($bundleItem->model instanceof VariationInfo) {
                $data['short_desc'] = $bundleItem->model->product->short_desc;
                $data['heb_title'] = $bundleItem->model->product->heb_title;
                $data['heb_short_desc'] = $bundleItem->model->product->heb_short_desc;
            }
            $data['quantity'] = $bundleItem->quantity;
            return $data;
        })->filter();
    }

    public function deductQuantity($quantity)
    {
        $this->bundleItems->each(function ($item) use ($quantity) {
            $item->model->deductQuantity($item->quantity * $quantity);
        });
    }

    public function removeFromBags()
    {
        $this->bundleItems->each(function ($item) {
            $item->model->removeFromBags();
        });
    }

    public function getExtendedDuration()
    {
        $extendedDurtion = 0;
        $bundle = $this;

        $bundle->bundleItems->each(function ($item) use (&$extendedDurtion) {
            $product = $item->model;
            $extendedDurtion += $product->duration ? ($product->duration + 1) : 0;
            if ($product->release_date) {
                $extendedDurtion += now()->isBefore($product->release_date) ? (now()->diffInDays(
                        $product->release_date
                    ) + 1) : 0;
            }
        });
        return $extendedDurtion;
    }

    public function setFormattedProductsAttribute($value)
    {
        //see BundleObserver
        return;
        $items = collect(json_decode($value))->map(function ($item) {
            switch ($item->type) {
                case 'product':
                    $type = 'App\Product';
                    break;

                case 'variation':
                    $type = 'App\VariationInfo';
                    break;
            }
            return BundleItem::make([
                'model_type' => $type,
                'model_id' => $item->id,
                // 'quantity' => $item->quantity,
                'quantity' => 1,
            ]);
        });
        $this->bundleItems()->delete();
        $this->bundleItems()->saveMany($items);
    }

    public function filters()
    {
        return $this->belongsToMany(FilterItem::class, 'filter_item_bundle')
            ->withTimestamps();
    }

    public function apply()
    {
        return $this->morphMany(CodeApply::class, 'applies');
    }

    public function models()
    {
        return $this->morphMany(Discount::class, 'model');
    }

    public function getFilterIdsAttribute()
    {
        return $this->filters->pluck('id');
    }

    public function setFilterIdsAttribute($value)
    {
        return;
    }

    public function getCategoriesIdsAttribute()
    {
        return $this->categories->pluck('id');
    }

    public function setCategoriesIdsAttribute($value)
    {
        return;
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);

        $this->addMediaConversion('thumbnail')
            ->width(100);

        $this->addMediaConversion('grid')
            ->width(250);

        $this->addMediaConversion('large')
            ->width(500);

        $this->addMediaConversion('lightbox')
            ->width(1500);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('media');
    }

    public function getFakePriceAttribute()
    {
        return $this->getFormattedProductsAttribute()
            ->pluck('price')
            ->sum();
    }

    public function getPathAttribute()
    {
        return "/bundles/{$this->slug}/{$this->id}";
    }

    public function scopeInactive($query)
    {
        $now = now();
        $query->where(function ($query) use ($now) {
            $query->where('visibility', false)->orWhere('publish', '>=', $now);
        });
        return $query;
    }

    public function scopeActive($query)
    {
        $now = now();

        $query->where('visibility', true);

        $query->where(function ($query) use ($now) {
            $query->where('publish', null)->orWhere('publish', '<', $now);
        });

        // $query ->where(function ($query) use ($now){
        //     $query->where('expire', null)->orWhere('expire', '>', $now);
        // });
        return $query;
    }

    public function getActiveAttribute()
    {
        if ($this->visibility && (!$this->publish || $this->publish < now())) {
            return true;
        }
        return false;
    }

    public function getSearchableAttribute()
    {
        return $this->active && $this->max > 0;
    }

    public function getBreadcrumbsAttribute()
    {
        $array = [['name' => $this->title]];

        if ($category = $this->categories->first()) {
            if ($item = MenuItem::where('value', $category->id)->where('class', 'App\Classes\Categories')->first()) {
                $array[] = ['name' => $item->name, 'path' => $item->customValue];
                $this->addParent($item, $array);
            }
        }

        return array_reverse($array);
    }

    private function addParent($item, &$array)
    {
        if ($item->parent_id) {
            if ($parent = MenuItem::find($item->parent_id)) {
                $array[] = ['name' => $parent->name, 'path' => $parent->customValue];
                $this->addParent($parent, $array);
            }
        }
    }

    public function getExcludeFreeShippingAttribute()
    {
        return $this->bundleItems->map(function ($item) {
            return $item->model->exclude_free_shipping;
        })->contains(true);
    }

    public function getExcludeFromReturnsAttribute()
    {
        return $this->bundleItems->map(function ($item) {
            return $item->model->exclude_from_returns;
        })->contains(true);
    }

    public function getShippableAttribute()
    {
        return $this->getItemType() == 'physical' || $this->getItemType() == 'both';
    }

    public function updateSearchFeild()
    {
        $bundle = $this;
        $bundle->withoutEvents(function () use ($bundle) {
            $bundle->update(['search' => $bundle->getModelSwiftypeTransformed()]);
        });
    }

    public function getFrontEndAttribute($quantity = 1)
    {
        $data = [
            'type' => 'bundle',
            'item_type' => $this->getItemType(),
            'id' => $this->id,
            'list_id' => $this->lists_id,
            'school_id' => data_get($this, 'lists.school.id'),
            'media' => $this->lists_id ? $this->lists->getFirstMediaUrl('media') : data_get(
                $this,
                'media_urls.0.thumbnail'
            ),
            'path' => $this->path,
            'price' => $this->price,
            'title' => $this->title,
            'products' => $this->getFrontEndProductsAttribute(),
            'quantity' => $quantity,
            'weight' => $this->weight,
            'max' => $this->max,
            'exclude_free_shipping' => $this->exclude_free_shipping,
            'exclude_from_returns' => $this->exclude_from_returns,
            'sku' => $this->sku,
            'extended_duration' => $this->getExtendedDuration(),
        ];

        return $data;
    }

    public function getItemType()
    {
        $products = $this->getFrontEndProductsAttribute();
        if (collect($products)->every(function ($item) {
            return $item['item_type'] == 'physical';
        })) {
            return 'physical';
        } elseif (collect($products)->every(function ($item) {
            return $item['item_type'] == 'digital';
        })) {
            return 'digital';
        } elseif (collect($products)->every(function ($item) {
            return $item['item_type'] == 'service';
        })) {
            return 'service';
        } else {
            return 'both';
        }
    }

    public function getWeightAttribute()
    {
        return $this->bundleItems->map(function ($item) {
            return optional($item->model)->weight;
        })->sum();
    }

    public function getBillableWeightAttribute()
    {
        return $this->bundleItems->map(function ($item) {
            return $item->model->billable_weight;
        })->sum();
    }

    public function getModelSwiftypeTransformed()
    {
        return collect($this->getAttributes())->only([
            'title',
            'heb_title',
            'barcode',
            'sku',
            'item_type',
        ])->merge([
            'id' => 'B' . $this->id,
            'path' => $this->path,
            'image' => $this->getFirstMediaUrl('media', 'grid'),

            'description' => strip_tags($this->description),
            'short_desc' => strip_tags($this->short_desc),
            'heb_description' => strip_tags($this->heb_description),
            'heb_short_desc' => strip_tags($this->heb_short_desc),

            'price' => $this->price,
            'fake_price' => $this->fake_price,

            'filters' => $this->filters->count() > 0 ? collect($this->filters->toArray())->map(function ($value) {
                return $value['parent'] . '_' . $value['name'];
            })->flatten() : null,

            'questions' => $this->filters->count() > 0 ? collect($this->filters)->map(function ($value) {
                return $value->filter['name'] . '_' . $value->filter['info'];
            })->flatten()->unique() : null,

            'categories' => $this->categories->count() > 0 ? $this->categories->map(function ($category) {
                return $category->name;
            }) : null,
        ]);
    }
}
