<?php

namespace App;

use <PERSON>tie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class GiftNotesSetting extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    protected $appends = [
        'picture',
        'message',
    ];

    protected $hidden = [
        'note',
    ];

    public function getMessageAttribute()
    {
        return auth()->user()
            ? str_replace('{{name}}', auth()->user()->name ?? '', $this->attributes['note'])
            : '';
    }

    public function getPictureAttribute()
    {
        return $this->getFirstMediaUrl('image');
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image')
            ->singleFile();
    }
}
