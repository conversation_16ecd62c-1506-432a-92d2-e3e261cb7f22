<?php

namespace App\Providers;

use App\Bundle;
use App\Observers\BundleObserver;
use App\Observers\OrderObserver;
use App\Observers\ProductObserver;
use App\Observers\VendorObserver;
use App\Order;
use App\Product;
use App\Vendor;
use Carbon\CarbonInterface;
use Carbon\Translator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use Psr\Log\LoggerInterface;

class AppServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        Translator::get()->setTranslations([
            'first_day_of_week' => CarbonInterface::SUNDAY,
            'last_day_of_week' => CarbonInterface::SATURDAY,
        ]);
        Product::observe(ProductObserver::class);
        Order::observe(OrderObserver::class);
        Bundle::observe(BundleObserver::class);
        Vendor::observe(VendorObserver::class);
    }

    public function register(): void
    {
        if ($this->app->environment('local') && class_exists(\Laravel\Telescope\TelescopeServiceProvider::class)) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }

        $this->app->alias('bugsnag.multi', Log::class);
        $this->app->alias('bugsnag.multi', LoggerInterface::class);
    }
}
