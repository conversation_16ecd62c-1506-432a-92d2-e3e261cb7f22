<?php

namespace App\Providers;

use App\Admin;
use App\Category;
use App\Creator;
use App\Customer;
use App\Http\Controllers\Admin\CategoriesController;
use App\Http\Controllers\Admin\FiltersController;
use App\Http\Controllers\Admin\ModifyOrderController;
use App\Http\Controllers\Admin\OrdersController;
use App\Http\Controllers\Admin\PathsController;
use App\Http\Controllers\Admin\ReturnsController;
use App\Http\Controllers\Admin\SearchController;
use App\Http\Controllers\Admin\SplitOrderController;
use App\Http\Controllers\Admin\TagsController;
use App\Http\Controllers\Api\CancelOrdersController;
use App\Http\Controllers\PosController;
use App\Http\Controllers\PosOnlineController;
use App\Http\Controllers\ProcessReturnController;
use App\InventoryDeductionUpdate;
use App\Nova\Dashboards\Main;
use App\Order;
use App\Product;
use App\ProductType;
use App\VariationInfo;
use App\Vendor;
use Capitalc\CsvImport\CsvImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Route;
use Laravel\Nova\Menu\MenuItem;
use Laravel\Nova\Menu\MenuSection;
use Laravel\Nova\Nova;
use Laravel\Nova\NovaApplicationServiceProvider;
use Outl1ne\MenuBuilder\MenuBuilder;
use PhpJunior\NovaLogViewer\Tool;

class NovaServiceProvider extends NovaApplicationServiceProvider
{

    public function boot()
    {
        parent::boot();

        Nova::notificationPollingInterval(300);

        Nova::mainMenu(function (Request $request) {
            return [
                MenuSection::dashboard(Main::class)->icon('chart-bar'),

                MenuSection::make('Customers', [
                    MenuItem::resource(\App\Nova\Bag::class),
                    MenuItem::resource(\App\Nova\Customer::class),
                    MenuItem::resource(\App\Nova\Address::class),
                    MenuItem::resource(\App\Nova\GiftCard::class),
                    MenuItem::resource(\App\Nova\GiftNote::class),
                    MenuItem::resource(\App\Nova\Group::class),
                    MenuItem::resource(\App\Nova\Order::class),
                    MenuItem::resource(\App\Nova\Returns::class),
                    MenuItem::resource(\App\Nova\RouteOrder::class),
                ])->icon('user')->collapsable(),

                MenuSection::make('Display', [
                    MenuItem::resource(\App\Nova\Page::class),
                ])->icon('eye')->collapsable(),

                MenuSection::make('Settings', [
                    MenuItem::resource(\App\Nova\Setting::class),
                    MenuItem::resource(\App\Nova\Admin::class),
                    MenuItem::resource(\App\Nova\Discount::class),
                    MenuItem::resource(\App\Nova\Export::class),
                    MenuItem::resource(\App\Nova\GiftCardSettings::class),
                    MenuItem::resource(\App\Nova\GiftNotesSetting::class),
                    MenuItem::resource(\App\Nova\GiftOption::class),
                    MenuItem::resource(\App\Nova\Personalize::class),
                    MenuItem::resource(\App\Nova\Redirect::class),
                ])->icon('cog')->collapsable(),

                MenuSection::make('Products', [
                    MenuItem::resource(\App\Nova\Product::class),
                    MenuItem::resource(\App\Nova\ProductType::class),
                    MenuItem::resource(\App\Nova\AutomatedCategory::class),
                    MenuItem::resource(\App\Nova\Category::class),
                    MenuItem::resource(\App\Nova\Bundle::class),
                    MenuItem::resource(\App\Nova\Creator::class),
                    MenuItem::resource(\App\Nova\Filter::class),
                    MenuItem::resource(\App\Nova\Label::class),
                    MenuItem::resource(\App\Nova\Link::class),
                    MenuItem::resource(\App\Nova\Vendor::class),
                ])->icon('shopping-cart')->collapsable(),

                MenuSection::make('Subscriptions', [
                    MenuItem::resource(\App\Nova\Recurring::class),
                    MenuItem::resource(\App\Nova\RecurringSetting::class),
                    MenuItem::resource(\App\Nova\Subscription::class),
                    MenuItem::resource(\App\Nova\SubscriptionGroup::class),
                    MenuItem::resource(\App\Nova\SubscriptionType::class),
                ])->icon('calendar')->collapsable(),

                MenuSection::make('Shipping', [
                    MenuItem::resource(\App\Nova\ShippingOption::class),
                    MenuItem::resource(\App\Nova\ShippingOptionShippingZone::class),
                    MenuItem::resource(\App\Nova\ShippingZone::class),
                    MenuItem::resource(\App\Nova\ClosedDay::class),
                    MenuItem::resource(\App\Nova\FulfillmentSchematic::class),
                    MenuItem::resource(\App\Nova\InternationalShippingZone::class),
                    MenuItem::resource(\App\Nova\Route::class),
                ])->icon('truck')->collapsable(),

                MenuSection::make('School Supplies', [
                    MenuItem::resource(\App\Nova\Lists::class),
                    MenuItem::resource(\App\Nova\School::class),
                ])->icon('academic-cap')->collapsable(),

                MenuSection::make('Other', [
                    MenuItem::resource(\App\Nova\PosMessage::class),
                ])->icon('information-circle')->collapsable(),

                MenuSection::make('Import')->icon('cloud-upload')->path('/csv-import'),

            ];
        });

        Nova::footer(function ($request) {
            return view('vendor.nova.partials.footer');
        });

        Nova::style('admin', public_path('css/admin.css'));
        Nova::style('cp', public_path('css/cp.css'));
        Nova::style('components', public_path('css/components.css'));
        // Nova::script('admin', public_path('js/admin.js'));
        Nova::script('jquery', public_path('js/jquery.js'));
        Nova::script('hotjar', public_path('js/hotjar.js'));
        Nova::script('dirrty', public_path('js/dirrty.js'));
        Nova::userTimezone(function (Request $request) {
            return 'UTC';
        });
    }


    protected function routes(): void
    {
        Nova::routes()
            ->withAuthenticationRoutes(default: true)
            ->withPasswordResetRoutes()
            ->register();

        Route::Group([
            'middleware' => config('nova.api_middleware', []),
            'prefix' => 'nova-custom-api',
        ], function () {
            // Route::get('/productVariations/{product}', function() {
            //     return ProductsController::productVariations($product);
            // });
            Route::get(
                '/productVariations/{product}',
                '\\App\\Http\\Controllers\\Admin\\ProductsController@productVariations'
            );

            Route::get('/menu_tree', function () {
                return CategoriesController::menuTree();
            });
            Route::get('/category_tree', function () {
                return CategoriesController::categoryTree();
            });
            Route::get('/categories/{ids}/filters', function ($ids) {
                return CategoriesController::filters($ids);
            });
            Route::get('/categories/allfilteroptions', function () {
                return CategoriesController::allFilters();
            });
            Route::get('/paths', function () {
                return PathsController::index();
            });
            Route::get('/tags', function () {
                return TagsController::index();
            });
            Route::get('/filter/{filter}/items', function ($filter) {
                return FiltersController::index($filter);
            });
            Route::get('/search/only_products', function () {
                return SearchController::only_products();
            });
            Route::get('/search/products', function () {
                return SearchController::products();
            });
            Route::get('/search/categories', function () {
                return SearchController::categories();
            });
            Route::get('/search/vendors', function () {
                return SearchController::vendors();
            });
            Route::get('/search/creators', function () {
                return SearchController::creators();
            });
            Route::get('/search/filters', function () {
                return SearchController::filters();
            });
            Route::get('/search/customers', function () {
                return SearchController::customers();
            });
            Route::get('/search/groups', function () {
                return SearchController::groups();
            });
            Route::get('/search/creator_delayed', function () {
                return SearchController::creator_delayed();
            });
            Route::get('/search/vendor_delayed', function () {
                return SearchController::vendor_delayed();
            });
            Route::get('/search/filter_delayed', function () {
                return SearchController::filter_delayed();
            });
            Route::get('/search/label_delayed', function () {
                return SearchController::label_delayed();
            });
            Route::post('/orders/update/{id}', function ($id) {
                return OrdersController::update($id);
            });
            Route::post('/orders/add-to-account/{id}', function ($id) {
                return OrdersController::addToAccount($id);
            });
            Route::post('/returns/update/{id}', function ($id) {
                return ReturnsController::update($id);
            });
            Route::post('/returns/process/{id}', function ($id) {
                return ReturnsController::process($id);
            });
            Route::post('/processBreakDown', function (Request $request) {
                return ProcessReturnController::process($request);
            });
            Route::post('/refund', function (Request $request) {
                return OrdersController::refundAmount($request);
            });
            Route::post('/capture', function (Request $request) {
                return OrdersController::captureCharge($request);
            });
            Route::post('/resendOrder', function (Request $request) {
                return PosController::CreateOrder(Order::find($request->id));
            });
            Route::post('/recancelOrder', function (Request $request) {
                return PosController::CancelOrder(Order::find($request->id));
            });
            Route::post('/resendOnlineOrder', function (Request $request) {
                return PosOnlineController::CreateOrder(Order::find($request->id));
            });
            Route::post('/recancelOnlineOrder', function (Request $request) {
                return PosOnlineController::CancelOrder(Order::find($request->id));
            });
            Route::post('/update-inventory-deduct', function (Request $request) {
                $order = Order::find($request->orderId);
                $originalProduct = null;
                $products = collect($order->products)->map(function ($product) use ($request, &$originalProduct) {
                    if (data_get($product, 'id') == data_get($request, 'product.id') && data_get(
                            $product,
                            'type'
                        ) == data_get($request, 'product.type') && data_get($product, 'item_key') == data_get(
                            $request,
                            'product.item_key'
                        )) {
                        $originalProduct = $product;
                        $product['store_deduct'] = data_get($request, 'product.store_deduct');
                        $product['website_deduct'] = data_get($request, 'product.website_deduct');
                    }
                    return $product;
                });
                $order->withoutEvents(function () use ($order, $products) {
                    $order->update(['products' => $products]);
                });
                InventoryDeductionUpdate::create([
                    'order_id' => $order->id,
                    'admin_by' => auth()->user()->id,
                    'origianl_product' => $originalProduct,
                    'updated_product' => $request->product,
                    'product_id' => data_get($request, 'product.id'),
                    'product_type' => data_get($request, 'product.type'),
                ]);
                PosController::CreateOrder($order);
                PosOnlineController::CreateOrder($order);
            });
            Route::post('/shipped', function (Request $request) {
                return OrdersController::markAsShipped($request);
            });
            Route::post('/delivered', function (Request $request) {
                return OrdersController::markAsDelivered($request);
            });
            Route::post('/cancel', function (Request $request) {
                return (new CancelOrdersController)->whole($request->orderId);
            });
            Route::post('/modifyorder/breakdown/{id}', function ($id, Request $request) {
                return ModifyOrderController::Breakdown($id, $request);
            });
            Route::post('/modifyorder/submit/{id}', function ($id, Request $request) {
                return ModifyOrderController::Submit($id, $request);
            });
            Route::post('/splitorder/submit/{id}', function ($id, Request $request) {
                return SplitOrderController::splitOrder($id, $request);
            });
            Route::post('/categories_options', function () {
                return Category::get(['id', 'name'])->map(function ($category) {
                    return [
                        'display' => $category->name,
                        'value' => $category->id
                    ];
                });
            });
            Route::post('/vendors_options', function () {
                return Vendor::get(['id', 'name'])->map(function ($vendor) {
                    return [
                        'display' => $vendor->name,
                        'value' => $vendor->id
                    ];
                });
            });
            Route::post('/creator_options', function () {
                return Creator::get(['id', 'name'])->map(function ($creator) {
                    return [
                        'display' => $creator->name,
                        'value' => $creator->id
                    ];
                });
            });
            Route::post('/digital_vendors_options', function () {
                $vendor_ids = Product::where('item_type', 'digital')->get('vendor_id')->map->vendor_id->unique(
                )->filter()->values()
                    ->merge(
                        VariationInfo::where('item_type', 'digital')->get('product_json')->map(
                            function ($product) {
                                return data_get($product, 'product_json.vendor_id');
                            }
                        )->unique()->filter()->values()
                    )->unique();
                return Vendor::find($vendor_ids)->map(function ($vendor) {
                    return [
                        'display' => $vendor->name,
                        'value' => $vendor->id
                    ];
                });
            });
            Route::get('/products_type/{type}', function ($type) {
                if ($type == 'Product') {
                    return DB::table('products')->select('id', 'title')->get()->map(function ($product) {
                        return [
                            'display' => $product->title,
                            'value' => $product->id
                        ];
                    });
                }
                return DB::table('variation_infos')->select('id', 'product_json')->get()->map(function ($product) {
                    return [
                        'display' => data_get(json_decode($product->product_json), 'title') . ' - ' . $product->id,
                        'value' => $product->id
                    ];
                });
            });
            Route::get('/customer/{customer}/addresses', function ($customer) {
                return Customer::find($customer)->addresses->map(function ($address) {
                    return [
                        'display' => $address->address_line_1,
                        'value' => $address->id
                    ];
                });
            });
            Route::get('/customer/{customer}/payments', function ($customer) {
                return Customer::find($customer)->payments->map(function ($payment) {
                    return [
                        'display' => 'Card ending in ' . $payment->last_four,
                        'value' => $payment->id
                    ];
                });
            });
            Route::get('/product-types/{id}', function ($id) {
                return ProductType::select(['id', 'name', 'creator_type'])->find($id);
            });
            Route::post(
                'products/{slug?}/{product}/duplicate',
                '\\App\\Http\\Controllers\\Admin\\ProductsController@duplicate'
            );
        });
    }

    /**
     * Register the Nova gate.
     *
     * This gate determines who can access Nova in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewNova', function ($user) {
            return in_array($user->email, Admin::all()->pluck('email')->toArray());
        });
    }

    protected function dashboards(): array
    {
        return [
            new Main,
        ];
    }


    public function tools()
    {
        $user = auth('admin')->user();
        if ($user && $user->role == 'Super Admin') {
            return [
                new CsvImport,
                new MenuBuilder,
                new Tool()
            ];
        } elseif ($user && $user->role == 'Order Fulfiller') {
            return [
                new CsvImport,
            ];
        } else {
            return [];
        }
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
