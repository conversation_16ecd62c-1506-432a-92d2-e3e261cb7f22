<template>
    <div
        v-show="show"
        id="chart-container"
        class="chart-container fadeIn animated faster"
    >
        <div class="gift_popup_box">
            <div class="gpb_head">
                <p class="gpb_head_ttl">Setup your Personalization</p>
                <div class="flex">
                    <button
                        :style="'margin-right:5px;'"
                        class="small_btn btn_style btn_follow"
                        @click="close()"
                    >
                        Cancel
                    </button>
                    <button
                        class="small_btn btn_style btn_following"
                        @click="save"
                    >
                        Save
                    </button>
                </div>
            </div>
            <div class="gpb_body" v-if="show">
                <personlized-options
                    v-if="options.length > 0"
                    :options="options"
                    :selected="selectedOptions.options"
                    ref="options"
                />

                <personlized-input
                    v-if="inputs.length > 0"
                    :options="inputs"
                    :selected="selectedOptions.single_line"
                    ref="input"
                />

                <personlized-textarea
                    v-if="textArea.length > 0"
                    :options="textArea"
                    :selected="selectedOptions.multi_line"
                    ref="textarea"
                />
            </div>
        </div>
    </div>
</template>

<script>
import PersonlizedOptions from './PersonlizedOptions.vue';
import PersonlizedInput from './PersonlizedInput.vue';
import PersonlizedCheckbox from './PersonlizedCheckbox.vue';
import PersonlizedTextarea from './PersonlizedTextarea.vue';
import state from '../../state';
import { mapActions, mapGetters } from 'vuex';

export default {
    components: {
        PersonlizedTextarea,
        PersonlizedCheckbox,
        PersonlizedInput,
        PersonlizedOptions,
    },
    props: {
        personalization: {
            type: Object,
            default: {},
        },
        item_key: {
            type: String,
            default: null,
        },
    },
    data() {
        return {
            state,
            show: false,
            options: [],
            inputs: [],
            textArea: [],
            optionsCom: {},
            inputsCom: {},
            textAreaCom: {},
            selectedOptions: {
                options: [],
                single_line: [],
                multi_line: [],
            },
        };
    },
    computed: {
        ...mapGetters(['products']),
        isValid() {
            let valid = true;
            if (this.optionsCom) {
                valid = valid && this.optionsCom.canContinue;
            }
            if (this.inputsCom) {
                valid = valid && this.inputsCom.canContinue;
            }
            if (this.textAreaCom) {
                valid = valid && this.textAreaCom.canContinue;
            }
            if (this.$refs.correct) {
                valid = valid && this.$refs.correct.correct;
            }

            return valid;
        },
    },
    mounted() {
        this.initPersonalization();
    },
    updated() {
        this.optionsCom = this.$refs.options;
        this.inputsCom = this.$refs.input;
        this.textAreaCom = this.$refs.textarea;
    },
    methods: {
        ...mapActions(['updatePersonalization']),
        initPersonalization(){
            const personalization = JSON.parse(JSON.stringify(this.personalization));
            this.options = personalization.options.map((o) => {
                return {
                    key: o.key.key,
                    attributes: {
                        instructions: o.key.instructions,
                        name: o.key.name,
                        price: o.key.price,
                        required: o.key.required,
                        layout: o.layout,
                        options: o.key.options.map((op) => {
                            return {
                                key: op.key,
                                attributes: {
                                    name: op.name,
                                    picture: op.picture,
                                },
                            };
                        }),
                    },
                };
            });
            this.inputs = personalization.single_line.map((o) => {
                return {
                    key: o.key.key,
                    attributes: {
                        instructions: o.key.instructions,
                        name: o.key.name,
                        price: o.key.price,
                        required: o.key.required,
                        layout: o.layout,
                        character_limit: o.key.character_limit,
                        place_holder: o.key.place_holder,
                    },
                };
            });
            this.textArea = personalization.multi_line.map((o) => {
                return {
                    key: o.key.key,
                    attributes: {
                        instructions: o.key.instructions,
                        name: o.key.name,
                        price: o.key.price,
                        required: o.key.required,
                        layout: o.layout,
                        character_limit: o.key.character_limit,
                        line_limit: o.key.max_lines,
                        place_holder: o.key.place_holder,
                    },
                };
            });
            this.selectedOptions = personalization;
        },
        open() {
            this.initPersonalization();
            this.$nextTick(() => {
                this.show = true;
            });
        },
        close(event) {
            this.show = false;
        },
        async save() {
            if (!this.isValid) return;

            if (this.optionsCom) {
                this.selectedOptions.options = this.optionsCom.selectedOptions;
            }

            if (this.inputsCom) {
                this.selectedOptions.single_line =
                    this.inputsCom.selectedInputs;
            }

            if (this.textAreaCom) {
                this.selectedOptions.multi_line =
                    this.textAreaCom.selectedInputs;
            }
            await this.updatePersonalization({
                item_key: this.item_key,
                personalization: JSON.parse(JSON.stringify(this.selectedOptions)),
            });
            this.close();
        },
    },
};
</script>

<style scoped>
.gift-opt-name-wrp {
    display: flex;
    justify-content: space-between;
    width: calc(100% - 87px);
    align-items: center;
    font-size: 21px;
    font-weight: 500;
}

.gift-opt-desc {
    font-size: 15px;
    font-weight: 300;
    line-height: 27px;
}

@media screen and (max-width: 768px) {
    .gift-opt-name-wrp {
        flex-direction: column;
        font-size: 15px;
    }

    .gift-opt-desc {
        line-height: 18px;
        font-size: 13px;
    }
}

.error {
    border-color: #f66 !important;
}
.char-error {
    color: #f66;
}
.chart-container {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.71);
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    overflow: auto;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
}
.gift_popup_box {
    margin-top: 17px;
    margin-bottom: 30px;
    position: absolute;
}
.gis_textarea {
    position: relative;
    display: flex;
    flex-direction: column;
}
.char-wrapper {
    font-size: 13px;
    text-align: end;
    margin-top: 5px;
}
.keyboard-action {
    position: absolute;
    width: 90px;
    height: 20px;
    /* background-color: #707070; */
    /* border-radius: 4px; */
    right: 10px;
    top: 10px;
    /* margin-top: 7px; */
    /* text-align: center;
    color: #FFFFFF;
    font-size: 14px; */
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.heb-keyboard-icon {
    height: 13px;
}
.gis_ta {
    font-weight: 300;
    padding-top: 25px;
}
.action-icon {
    transition: 500ms ease;
    fill: #b7b7b7;
    fill-rule: evenodd;
}
.action-active {
    fill: #016145 !important;
}
.rtl {
    direction: rtl;
}
.center {
    text-align: center;
}
.ltr {
    /* direction: ltr; */
}
.gpd_option:hover {
    border: 1px solid var(--gold);
}
.gpd_option.gpd_option_selected:hover {
    border: 2px solid var(--gold);
}
.btn_follow {
    transition: 500ms ease;
}
.btn_follow:hover {
    border: 1px solid var(--button_and_green_text);
    background-color: var(--button_and_green_text);
    color: #fff;
}
.gift-check-msg {
    margin-top: 20px;
}
.gift-check-msg p:first-child {
    color: #016145;
}
.gift-check-msg p:last-child {
    color: orange;
}
.btn-cancel {
    margin-top: 10px;
    text-align: center;
    cursor: pointer;
}
.ex-keyboard {
    position: absolute;
    right: 0;
    top: -30px;
}
</style>
