import laravel from 'laravel-vite-plugin';
import { defineConfig } from 'vite';
import { createVuePlugin } from 'vite-plugin-vue2'; // for vue version 2.6
import process from 'process';

export default defineConfig({
    plugins: [
        laravel({
                input: ['resources/js/app.js', 'resources/sass/app.scss'],
                refresh: true,
            }),
        createVuePlugin(),
    ],
    resolve: {
        alias: {
            vue: 'vue/dist/vue.esm.js',
            '@': '/resources/js'
        },
        extensions: ['.js', '.vue', '.json']
    },
    define: {
        'process.env': process.env,
    },
});
