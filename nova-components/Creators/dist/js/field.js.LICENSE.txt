/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> <http://feross.org>
 * @license  MIT
 */

/*!
 * vue3-treeselect v0.5.0 | (c) 2024 <PERSON>
 * Released under the MIT License.
 * https://vue3-treeselect.js.org/
 */

/*!
 * vuex v4.1.0
 * (c) 2022 Evan You
 * @license MIT
 */

/*! ieee754. BSD-3-Clause License. Feross <PERSON> <https://feross.org/opensource> */

/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */

/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
 */
