<template>
    <DefaultField
        :field="field"
        :errors="errors"
        :show-help-text="showHelpText"
        :full-width-content="fullWidthContent"
    >
        <template #field>
            <div class="wrapper items-center">
                <div :id="field.name" class="left-wrapper">
                    <p>
                        {{ this.field.value }}
                    </p>
                </div>
                <div class="action-wrapper">
                    <p @click.prevent="resend()" class="dim action-btn">
                        Resend To POS
                    </p>
                </div>
            </div>
        </template>
    </DefaultField>
</template>

<script>
import { FormField, HandlesValidationErrors } from 'laravel-nova'
import axios from 'axios'

export default {
  mixins: [FormField, HandlesValidationErrors],

  props: ['resourceName', 'resourceId', 'field'],
    methods: {
      async resend (){
          try {
              let url = '/nova-custom-api/resendOrder';
              if (this.field.cancelled && !this.field.online) {
                  url = '/nova-custom-api/recancelOrder';
              }
              if (this.field.online && !this.field.cancelled) {
                  url = '/nova-custom-api/resendOnlineOrder';
              }
              if (this.field.online && this.field.cancelled) {
                  url = '/nova-custom-api/recancelOnlineOrder';
              }
              const response = await axios.post(url, {id:this.resourceId});
              if (response && response.data){
                  if(response.data === 'failed'){
                      Nova.error('Order could not be sent to POS');
                  } else {
                      Nova.success('Order has been sent to POS');
                  }
              }
          } catch (e) {
              Nova.error('Something went wrong');
          }
      },
  },
}
</script>

<style scoped>
.wrapper {
    display: flex;
    justify-content: space-between;
}
.action-wrapper {
    display: flex;
}
.action-btn {
    color: #4099de;
    cursor: pointer;
    font-size: 15px;
    margin-left: 20px;
}
.cu-info {
    color: #525860;
    font-size: 18px;
    margin-bottom: 15px;
    font-weight: 400;
}
</style>
