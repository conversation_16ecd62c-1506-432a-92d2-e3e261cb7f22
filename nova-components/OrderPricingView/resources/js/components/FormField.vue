<template>
    <DefaultField
        :field="field"
        :errors="errors"
        :show-help-text="showHelpText"
        :full-width-content="fullWidthContent"
    >
        <template #field>
            <div class="wrapper">
                <div
                    class="kvp-wrapper"
                    v-for="(val, key) in field.value"
                    :key="key"
                    v-show="val"
                    :class="{
                        'order-total':
                            key == 'Order Total' || key == 'Refund Total',
                    }"
                >
                    <div>{{ key }}</div>

                    <div
                        v-if="key == 'Discount' && val && val.discount"
                        class="flex_"
                    >
                        <div>{{ currency(val.discount.savings) }}</div>
                        <div>{{ val.discount.name }}</div>
                    </div>
                    <div class="flex_" v-else-if="key == 'Paid with'">
                        <a
                            style="cursor: pointer; color: #4099de"
                            class="dim"
                            :href="v.link"
                            v-for="(v, i) in val"
                            :key="i"
                        >
                            {{ v.method }}
                        </a>
                    </div>

                    <div v-else-if="typeof val != 'object'">
                        {{ currency(val) }}
                    </div>

                    <div v-else class="flex_">
                        <div v-for="(v, i) in val" :key="i">{{ v }}</div>
                    </div>
                </div>
            </div>
        </template>
    </DefaultField>
</template>

<script>
import { FormField, HandlesValidationErrors } from 'laravel-nova';

export default {
    mixins: [FormField, HandlesValidationErrors],

    props: ['resourceName', 'resourceId', 'field'],

    methods: {
        setInitialValue() {
            this.value = this.field.value || '';
        },
        fill(formData) {
            formData.append(this.fieldAttribute, this.value || '');
        },
        currency(value) {
            if (value) {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                }).format(value);
            }
        },
    },
};
</script>


<style scoped>
.wrapper{
    height: 100%;
    display: flex;
    align-items: center;
    color: #525860;
    font-size: 18px;
    font-weight: 500;
    flex-direction: column;
    width: 100%;
}
.kvp-wrapper{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 30px;
}
.flex_{
    display: flex;
    flex-direction: column;
}
.flex_ > div{
    margin-bottom: 15px;
}
.order-total{
    padding-top: 15px;
    font-weight: 600;
    border-top: #EFF1F4 solid 1px;
}
</style>
