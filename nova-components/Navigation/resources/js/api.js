export default {
  getItems(menu) {
    return window.axios
      .post('/nova-vendor/navigation/items', {
        menu: menu,
      })
      .then(response => response.data);
  },

  saveItems(menu, menuItems) {
    return window.axios
      .post('/nova-vendor/navigation/save-items', {
        menu: menu,
        items: menuItems,
      })
      .then(response => response.data);
  },

  create(menuItem) {
    return window.axios.post('/nova-vendor/navigation/new-item', menuItem).then(response => response.data);
  },

  edit(menu) {
    return window.axios.get('/nova-vendor/navigation/edit/' + menu).then(response => response.data);
  },

  update(menuItemId, menuItem) {
    return window.axios.post('/nova-vendor/navigation/update/' + menuItemId, menuItem).then(response => response.data);
  },

  destroy(menuItemId) {
    return window.axios.post('/nova-vendor/navigation/destroy/' + menuItemId).then(response => response.data);
  },

  getLinkTypes() {
    return window.axios.get('/nova-vendor/navigation/link-types').then(response => response.data);
  },
};
