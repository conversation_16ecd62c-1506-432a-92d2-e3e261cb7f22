<?php

namespace Capitalc\Navigation;

use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Capitalc\Navigation\Http\Middleware\Authorize;
use Capitalc\Navigation\Http\Resources\MenuResource;
use Laravel\Nova\Events\ServingNova;
use Laravel\Nova\Nova;

class MenuBuilderServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'navigation');

        $this->app->booted(function () {
            $this->routes();
        });

        $this->publishMigrations();

        Nova::serving(function (ServingNova $event) {
            //
        });

        Nova::resources([
            MenuResource::class,
        ]);
    }

    /**
     * Register the tool's routes.
     *
     * @return void
     */
    protected function routes()
    {
        if ($this->app->routesAreCached()) {
            return;
        }

        Route::middleware(['nova', Authorize::class])
            ->namespace('Capitalc\Navigation\Http\Controllers')
            ->prefix('nova-vendor/navigation')
            ->group(__DIR__ . '/../routes/api.php');
    }

    /**
     * Publish required migration
     */
    private function publishMigrations()
    {
        $this->publishes([
            __DIR__ . '/Migrations/create_menus_table.php.stub' => database_path('migrations/' . date('Y_m_d_His', time()) . '_create_menus_table.php'),
        ], 'navigation-builder-migrations');
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
