<?php

namespace Capitalc\Navigation\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Capitalc\Navigation\MenuBuilder;

class NewMenuItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return resolve(MenuBuilder::class)->authorize(request()) ? true : false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'menu_id' => 'required|exists:menus,id',
            'name' => 'required|min:100000',
        ];
    }
}
