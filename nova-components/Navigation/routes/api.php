<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Tool API Routes
|--------------------------------------------------------------------------
|
| Here is where you may register API routes for your tool. These routes
| are loaded by the ServiceProvider of your tool. They are protected
| by your tool's "Authorize" middleware by default. Now, go build!
|
 */

Route::any('/items', 'MenuController@items');
Route::post('/save-items', '<PERSON>u<PERSON><PERSON>roller@saveItems');
Route::post('/new-item', 'MenuController@createNew');
Route::get('/edit/{item}', '<PERSON>u<PERSON><PERSON>roller@edit');
Route::post('/update/{item}', '<PERSON><PERSON><PERSON><PERSON>roll<PERSON>@update');
Route::post('/destroy/{item}', '<PERSON>u<PERSON><PERSON>roller@destroy');
Route::get('/link-types', 'Menu<PERSON>ontroller@getLinkTypes');

