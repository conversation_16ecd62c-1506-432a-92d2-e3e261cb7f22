<template>
    <DefaultField
        :field="field"
        :errors="errors"
        :show-help-text="showHelpText"
        :full-width-content="fullWidthContent"
    >
        <template #field>
            <div v-if="field.value" class="wrapper">
                <div class="left-wrapper">
                    <a
                        class="cu-name"
                        :href="`/admin/resources/customers/${field.value.id}`">{{ field.value.name }}
                    </a>
                    <p class="cu-info">{{ field.value.email }}</p>
                    <p class="cu-info">{{ field.value.phone }}</p>
                </div>
                <div class="action-wrapper">
                    <p @click.prevent="copy()" class="action-btn">Copy</p>
                    <a
                        class="action-btn"
                        :href="`/admin/resources/customers/${field.value.id}/edit`">
                        Edit
                    </a>
                </div>
            </div>
        </template>
    </DefaultField>
</template>

<script>
import { FormField, HandlesValidationErrors } from 'laravel-nova';

export default {
    mixins: [FormField, HandlesValidationErrors],

    props: ['resourceName', 'resourceId', 'field'],

    methods: {
        setInitialValue() {
            this.value = this.field.value || '';
        },
        fill(formData) {
            formData.append(this.fieldAttribute, this.value || '');
        },
        copy() {
            var copyText = document.querySelector('.left-wrapper').textContent;
            const el = document.createElement('textarea');
            el.value = copyText;
            document.body.appendChild(el);
            el.select();
            document.execCommand('copy');
            document.body.removeChild(el);
            Nova.success('Content copied');
        },
    },
};
</script>

<style>
.wrapper {
    display: flex;
    justify-content: space-between;
}

.action-wrapper {
    display: flex;
}

.action-btn {
    color: #4099de;
    cursor: pointer;
    font-size: 15px;
    margin-left: 20px;
}

.cu-info,
.cu-name {
    color: #525860;
    font-size: 18px;
    margin-bottom: 15px;
    font-weight: 400;
}

.cu-name {
    color: #4099de;
    display: block;
}

.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 140px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 150%;
    left: 50%;
    margin-left: -75px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip .tooltiptext::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #555 transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}
</style>
