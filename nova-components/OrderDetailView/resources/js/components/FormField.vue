<template>
    <DefaultField
        :field="field"
        :errors="errors"
        :show-help-text="showHelpText"
        :full-width-content="fullWidthContent"
    >
        <template #field>
            <div class="wrapper">
                    <span
                        v-if="field.value && field.value.kvp"
                        style="width: 100%"
                    >
                        <div
                            class="kvp-wrapper"
                            v-for="(val, key) in value"
                            :key="key"
                        >
                            <div>{{ key }}</div>
                            <div>{{ val }}</div>
                        </div>
                    </span>
                <span v-else style="width: 100%">
                        <div
                            class="value-wrapper"
                            v-for="(val, index) in value"
                            :key="index"
                            v-html="val"
                        ></div>
                    </span>
            </div>
        </template>
    </DefaultField>
</template>

<script>
import { FormField, HandlesValidationErrors } from 'laravel-nova';

export default {
    mixins: [FormField, HandlesValidationErrors],

    props: ['resourceName', 'resourceId', 'field'],
    data() {
        return {
            value: '',
        };
    },
    methods: {
        setInitialValue() {
            this.value =
                this.field.value && this.field.value.kvp
                    ? this.field.value.kvp
                    : this.field.value;
        },
    },
};
</script>
