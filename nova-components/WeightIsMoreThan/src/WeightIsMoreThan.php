<?php

namespace Capitalc\WeightIsMoreThan;

use Illuminate\Http\Request;
use Lara<PERSON>\Nova\Filters\Filter;

class WeightIsMoreThan extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'weight-is-more-than';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query
            ->where('weight', '!=', null)
            ->where('weight', '>', (int)$value);
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function options(Request $request)
    {
        return [];
    }
}
