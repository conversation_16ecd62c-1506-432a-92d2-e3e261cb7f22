<?php

namespace Capitalc\SidebarNavigation;

use Illuminate\Support\ServiceProvider;
use Laravel\Nova\Events\ServingNova;
use Laravel\Nova\Nova;

class FieldServiceProvider extends ServiceProvider
{
    public function boot()
    {
        Nova::serving(function (ServingNova $event) {
            Nova::script('sidebar-navigation', __DIR__.'/../dist/js/field.js');
            Nova::style('sidebar-navigation', __DIR__.'/../dist/css/field.css');
        });
    }

    public function register()
    {
        //
    }
}
