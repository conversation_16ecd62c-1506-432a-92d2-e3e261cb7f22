<?php

namespace Capitalc\SidebarNavigation;

use Laravel\Nova\Fields\Field;

class SidebarNavigation extends Field
{
    public $component = 'sidebar-navigation';

    public function __construct($name, array $sections = [], $url = null)
    {
        parent::__construct($name);

        $this->withMeta(['sections' => $sections, 'url' => $url]);
        $this->hideFromIndex();
        $this->fillUsing(fn () => null);
        $this->resolve(fn () => null);
    }
}
