<template>
    <div v-if="isMounted">
        <Teleport :to="teleportTo">
            <div class="sidebar-navigation w-48 mr-4 shrink-0" ref="sidebarNavWrap">
                <div class="w-48" ref="sidebarNav">
                    <ul>
                        <li v-for="section in sections" :class="{ active: isActive(section.name) }" :key="section.name">
                            <a @click.prevent="scrollToElement(section)">{{ section.name }}</a>
                        </li>
                    </ul>
                </div>
            </div>
        </Teleport>
        <Teleport v-if="url" :to="headingTeleportTo">
            <div class="flex items-center gap-2">
                <a class="cursor-pointer" :href="url" target="_blank">
                    <Icon type="eye" solid />
                </a>
            </div>
        </Teleport>
    </div>
</template>

<script>
import { FormField, HandlesValidationErrors } from 'laravel-nova'

export default {
    mixins: [FormField, HandlesValidationErrors],
    props: ['resourceName', 'resourceId', 'field'],
    data() {
        return {
            isMounted: false,
            teleportTo: '',
            activeId: null,
            triggerOffsetTop: 0,
            offsetLeft: 0,
            sections: [],
            url: null,
            headingTeleportTo: '',
        };
    },
    mounted() {
        this.url = this.field.url;
        for (const name in this.field.sections) {
            const selector = this.field.sections[name];
            const element = document.querySelector(selector);
            if (element) {
                const top = element.getBoundingClientRect().top;
                this.sections.push({
                    selector,
                    name,
                    top
                });
            }
        }
        if(this.sections) {
            this.sections = this.sections.sort((a, b) => a.top - b.top);
        }
        this.$nextTick(() => {
            const form = document.querySelector('[dusk="content"] form');
            const headingTeleportTo = form.querySelector('h1');
            const content = form.parentElement;
            if (content) {
                content.classList.add('flex');
                content.classList.add('justify-between');
                form.classList.add('grow');
                this.teleportTo = content;
            }
            if (headingTeleportTo) {
                headingTeleportTo.classList.add('flex');
                headingTeleportTo.classList.add('items-center');
                headingTeleportTo.classList.add('gap-2');
                this.headingTeleportTo = headingTeleportTo;
            }
            this.isMounted = true;

            this.$nextTick(() => {
                const sidebar = this.$refs.sidebarNav;
                if (sidebar) {
                    this.triggerOffsetTop = sidebar.getBoundingClientRect().top + window.scrollY;
                    this.offsetLeft = sidebar.getBoundingClientRect().left;
                }
                window.addEventListener('scroll', this.handleScroll);
                window.addEventListener('resize', this.handleScroll);
            });
        });
    },
    beforeDestroy() {
        window.removeEventListener('scroll', this.handleScroll);
        window.removeEventListener('resize', this.handleScroll);
    },
    methods: {
        scrollToElement(section) {
            const element = document.querySelector(section.selector);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        },
        isActive(name) {
            return this.activeId === name;
        },
        handleScroll() {
            const sidebar = this.$refs.sidebarNav;
            const wrapper = this.$refs.sidebarNavWrap;
            if (!sidebar) return;
            const currentOffset = window.pageYOffset;

            if (currentOffset >= this.triggerOffsetTop - 20) {
                this.offsetLeft = wrapper.getBoundingClientRect().left;
                sidebar.style.position = 'fixed';
                sidebar.style.top = 20 + 'px';
                sidebar.style.left = this.offsetLeft + 'px';
            } else {
                sidebar.style.position = 'relative';
                sidebar.style.top = '';
                sidebar.style.left = '';
            }

            this.updateActiveSection();
        },
        updateActiveSection() {
            const scrollPosition = window.scrollY + 100;
            let activeSection = null;

            for (const section of this.sections) {
                const element = document.querySelector(section.selector);
                if (element) {
                    const top = element.getBoundingClientRect().top + window.scrollY;
                    if (scrollPosition >= top) {
                        activeSection = section.name;
                    }
                }
            }
            this.activeId = activeSection;
        }
    }
};
</script>

<style>
.sidebar-navigation{
    order: -1;
}
.sidebar-navigation a{
    display: block;
    padding: 1rem;
    text-decoration: none;
    background-color: rgb(var(--colors-gray-200));
    color: rgb(var(--colors-gray-600));
    margin-bottom: 1px;
    font-size: 1rem;
    cursor: pointer;
    font-weight: bold;
}
.sidebar-navigation a:hover{
    background-color: rgb(var(--colors-gray-300));
    color: rgb(var(--colors-gray-700));
}
.sidebar-navigation li:first-child a{
    border-radius: 0.5rem 0.5rem 0 0;
}
.sidebar-navigation li:last-child a{
    border-radius: 0 0 0.5rem 0.5rem;
}
.sidebar-navigation .active a{
    background-color: rgb(var(--colors-gray-400));
    color: rgb(var(--colors-white));
}
</style>
