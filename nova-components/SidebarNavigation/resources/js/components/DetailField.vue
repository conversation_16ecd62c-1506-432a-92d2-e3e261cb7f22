<template>
    <div v-if="isMounted">
        <Teleport v-if="url" :to="headingTeleportTo">
            <div class="flex items-center gap-2">
                <a class="rounded hover:bg-gray-200 dark:hover:bg-gray-800 focus:outline-none focus:ring v-popper--has-tooltip" :href="url" target="_blank">
                    <span class="cursor-pointer rounded text-sm font-bold focus:outline-none focus:ring ring-primary-200 dark:ring-gray-600 inline-flex items-center justify-center h-9 px-3">
                        <Icon type="eye" solid />
                    </span>
                </a>
            </div>
        </Teleport>
    </div>
</template>

<script>
import { FormField, HandlesValidationErrors } from 'laravel-nova'

export default {
    mixins: [FormField, HandlesValidationErrors],
    props: ['resourceName', 'resourceId', 'field'],
    data() {
        return {
            isMounted: false,
            url: null,
            headingTeleportTo: '',
        };
    },
    mounted() {
        this.url = this.field.url;
        const parent = this.$el.parentElement;
        if (parent) {
            parent.classList.add('hidden');
        }
        this.$nextTick(() => {
            const content = document.querySelector('[dusk="content"]');
            const editButton = content.querySelector('[dusk="edit-resource-button"]');
            if (editButton) {
                const actionsContainer = editButton.closest('div');
                const newDiv = document.createElement('div');
                newDiv.className = 'flex items-center gap-2';
                actionsContainer.insertBefore(newDiv, editButton);
                this.headingTeleportTo = newDiv;
            }
            this.isMounted = true;
        });
    },
};
</script>
