<script>
export default {
    name: 'DropdownAction',
    data() {
        return {
            component: 'div',
        };
    },
    mounted() {
        if (this.$attrs.href) {
            this.component = 'a';
        }
    },
};
</script>

<template>
    <component
        :is="component"
        class="block w-full text-left py-1 px-3 focus:outline-none rounded truncate whitespace-nowrap cursor-pointer hover:bg-gray-100  transition-colors"
    >
        <slot></slot>
    </component>
</template>

<style scoped></style>
