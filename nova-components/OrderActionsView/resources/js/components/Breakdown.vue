<template>
    <div>
        <div v-if="breakdown" class="wrapper">
            <div class="kvp-wrapper">
                    <div>Subtototal</div>
                    <div>{{currency(breakdown.sub_total)}}</div>
            </div>
            <div class="kvp-wrapper">
                    <div>Delivery Fee</div>
                    <div>{{currency(breakdown.shipping_amount)}}</div>
            </div>
            <div class="kvp-wrapper">
                    <div>Tax</div>
                    <div>{{currency(breakdown.tax_amount)}}</div>
            </div>
            <div class="kvp-wrapper">
                    <div>Discount</div>
                    <div>{{currency(breakdown.discount_amount)}}</div>
            </div>
            <div class="kvp-wrapper order-total">
                    <div>Refund Total</div>
                    <div>{{currency(breakdown.grand_total)}}</div>
            </div>
            <div class="kvp-wrapper">
                    <div>Refund to</div>
                    <div class="flex_">
                        <div v-for="(refund,index) in refundTo()" :key="index">
                            {{refund}}
                        </div>
                    </div>
            </div>
        </div>
    </div>
</template>

<script>
import _ from 'lodash'
export default {
    props:['breakdown'],

    methods:{
        refundTo(){
			let refund = []
			let c = _.get(this.breakdown,'payments.creditInfo')
			if(c && c.payment_type == 'creditCard'){
				refund.push(`${_.capitalize(c.type)} ... ${c.last_four}`)
			}
			if(c && c.payment_type == 'payPal'){
				refund.push(`Paypal`)
			}
			let g = _.get(this.breakdown,'payments.giftCard')
			if(g && g.length > 0){
				g.forEach(gift=>{
					refund.push(`Gift card ... ${gift.code.slice(-4)}`)
				})
			}
			return refund
		},
        currency(value){
            if(!value) value = 0;
            return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value)
        }
    },
}
</script>

<style scoped>
.wrapper{
    margin-top: 50px;
    height: 100%;
    display: flex;
    align-items: center;
    color: #525860;
    font-size: 18px;
    font-weight: 500;
    flex-direction: column;
    width: 100%;
}
.kvp-wrapper{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 30px;
}
.flex_{
    display: flex;
    flex-direction: column;
}
.flex_ > div{
    margin-bottom: 15px;
}
.order-total{
    padding-top: 15px;
    font-weight: 600;
    border-top: #EFF1F4 solid 1px;
}
</style>
