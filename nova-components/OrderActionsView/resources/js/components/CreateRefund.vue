<template>
<div v-if="show" class="rf-wrp">
  <div  class="ed-wrapper">
      <div class="ed-inner-wrapper">
          <input type="number"
                placeholder="Amount to refund"
                v-model="amount"
                class="w-full form-control form-input form-input-bordered"
          >
          <select
                placeholder="Type of refund"
                v-model="type"
                class="w-full form-control form-input form-input-bordered"
          >
                <option value="original">To original payment method</option>
                <option value="giftCard">Generate eGift Card</option>
          </select>

           <textarea
                placeholder="Message to customer"
                v-model="message"
                class="w-full form-control form-input form-input-bordered"
          ></textarea>
          <div class="ed-actions">
              <default-button component="div" @click="save()" id="save">Process Refund</default-button>
              <outline-button component="div" @click.prevent="close()">Cancel</outline-button>
          </div>
      </div>
  </div>
  </div>

</template>

<script>
import axios from 'axios'

export default {
    data(){
        return{
            order:{},
            show:false,
            amount:'',
            message:'',
            type:''
        }
    },
    methods:{

        open(data){
            this.show = true;
            this.order = data
        },
        save(){

           axios.post('/nova-custom-api/refund',{amount:this.amount,
                                          message:this.message.split('\n'),
                                          orderId:this.order.id,
                                          type:this.type}).then(resp=>{
                                              Nova.success('The money will be refunded and a confirmation will be sent out to the customer')
            })
            .catch(err=>{
                Nova.error(_.get(err.response.data,'message') || 'Something went wrong');
            })

            this.show = false;
            this.message = '';
            this.amount = '';
            this.type = '';

        },
        close(){
            this.show = false;
            this.message = '';
            this.amount = '';
            this.type = '';
        }
    }
}
</script>

<style scoped>
textarea {
    height: 150px;
    padding: .75rem;
    margin-bottom: 15px;
}
select {
    padding: .75rem;
    margin-bottom: 15px;
}
.rf-wrp{
    background-color: rgba(124,133,142,.7);
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
}
.ed-wrapper{
    background-color: rgba(255, 255, 255.1);
    z-index: 10;
    /* height: 500px; */
    /* position: fixed; */
    display: flex;
    justify-content: center;
    align-items: center;
    top: 220px;
    background-color: white;
    border: lightgray solid 1px;
    border-radius: 5px;
    padding: 30px;
    width: 600px;
}
.ed-inner-wrapper{
    padding: 20px;
    overflow: scroll;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.ed-inner-wrapper input{
    margin-bottom: 20px;
    width: 100%;
}
.ed-actions{
    display: flex;
    gap: 1rem;
    align-self: flex-end;
}
</style>
