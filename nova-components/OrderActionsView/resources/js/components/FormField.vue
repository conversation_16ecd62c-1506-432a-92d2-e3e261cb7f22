<template>
    <div id="update_wrapper" v-if="loaded && order">
        <Teleport
            class="btn-actions-wrapper"
            :to="teleportTo"
            v-if="teleportTo"
        >
            <Dropdown>
                <template #default>
                    <default-button component="div">Actions</default-button>
                </template>
                <template #menu>
                    <DropdownMenu width="auto">
                        <ScrollWrap :height="300">
                            <nav
                                class="px-1 divide-y divide-gray-100 dark:divide-gray-800 divide-solid"
                            >
                                <div class="py-1">
                                    <DropdownAction
                                        v-if="canCancel"
                                        @click="cancelOrder()"
                                        >Cancel Order
                                    </DropdownAction>
                                    <DropdownAction
                                        v-if="canStartReturn"
                                        @click="startReturn()"
                                        >Start a Return
                                    </DropdownAction>
                                    <DropdownAction
                                        :href="`/view-invoice/${resourceId}`"
                                        target="_blank"
                                        >Print Invoice
                                    </DropdownAction>
                                    <DropdownAction
                                        v-if="tracking && tracking.link"
                                        :href="tracking.link"
                                        target="_blank"
                                        >View Tracking Page
                                    </DropdownAction>
                                    <DropdownAction
                                        :href="emailTo"
                                        target="_blank"
                                        >Email Customer
                                    </DropdownAction>
                                    <DropdownAction @click="refundOrder"
                                        >Refund Charge
                                    </DropdownAction>
                                    <DropdownAction
                                        v-if="canCapture"
                                        @click="captureOrder"
                                        >Capture Charge
                                    </DropdownAction>
                                    <DropdownAction
                                        v-if="canShipped"
                                        @click="markAsShipped"
                                        >Mark As Shipped
                                    </DropdownAction>
                                    <DropdownAction
                                        v-if="canDelivered"
                                        @click="markAsDelivered"
                                        >Mark As Delivered
                                    </DropdownAction>
                                    <DropdownAction
                                        v-if="canReady"
                                        @click="markAsReady"
                                        >Mark As Ready
                                    </DropdownAction>
                                    <DropdownAction
                                        v-if="canPickedUp"
                                        @click="markAsPickedUp"
                                        >Mark As Picked Up
                                    </DropdownAction>
                                </div>
                            </nav>
                        </ScrollWrap>
                    </DropdownMenu>
                </template>
            </Dropdown>
        </Teleport>

        <warning ref="warning" />
        <create-return ref="createReturn" />
        <create-refund ref="createRefund" />
    </div>
</template>

<script>
import { FormField, HandlesValidationErrors } from 'laravel-nova';
import DropdownAction from './DropdownAction.vue';
import Warning from './Warning.vue';
import CreateReturn from './CreateReturn.vue';
import CreateRefund from './CreateRefund.vue';
import _ from 'lodash';

export default {
    mixins: [FormField, HandlesValidationErrors],

    props: ['resourceName', 'resourceId', 'field'],
    components: {
        CreateRefund,
        CreateReturn,
        DropdownAction,
        Warning,
    },
    data() {
        return {
            order: null,
            loaded: false,
            teleportTo: null,
            tracking: null,
        };
    },
    computed: {
        status() {
            if (this.order.status == 'paid' && this.order.shippable) {
                return 'Unfulfilled';
            }
            return _.capitalize(this.order.status);
        },
        paymentStatus() {
            return _.capitalize(this.order.payment_status);
        },
        emailTo() {
            const customer = this.order.customer;
            let name = customer.name;
            let url = `https://secure.helpscout.net/mailbox/aad50824129062a5/new-ticket/`;
            let urlName = `?name=${name[0]}${name[1] ? '+' + name[1] : ''}`;
            let email = `&email=${customer.email}`;
            let subject = `&subject=RE: Order ${this.resourceId}`;
            return url + urlName + email + subject;
        },
        canStartReturn() {
            let products = this.order.products;
            return (
                products.find((p) => p.returns_left > 0) &&
                this.order.status != 'cancelled'
            );
        },
        canCapture() {
            return (
                this.order.meta.captured != true &&
                this.order.status != 'cancelled'
            );
        },
        canShipped() {
            return (
                _.get(this.order.shipping, 'shippingType.delivery') &&
                this.order.status != 'shipped' &&
                this.order.status != 'delivered' &&
                this.order.status != 'cancelled' &&
                this.order.shippable
            );
        },
        canDelivered() {
            return (
                _.get(this.order.shipping, 'shippingType.delivery') &&
                this.order.status != 'delivered' &&
                this.order.status != 'cancelled' &&
                this.order.shippable
            );
        },
        canReady() {
            return (
                !_.get(this.order.shipping, 'shippingType.delivery') &&
                this.order.status != 'ready' &&
                this.order.status != 'picked up' &&
                this.order.status != 'cancelled' &&
                this.order.shippable
            );
        },
        canPickedUp() {
            return (
                !_.get(this.order.shipping, 'shippingType.delivery') &&
                this.order.status != 'picked up' &&
                this.order.status != 'cancelled' &&
                this.order.shippable
            );
        },
        canCancel() {
            return this.order.status == 'paid'; //this.order.status != 'delivered' && this.order.status != 'shipped' && this.order.status != 'cancelled'
        },
    },
    updated() {
        this.tracking = _.get(this.order, 'shipping.tracking');
    },
    mounted() {
        this.order = this.field.order;
        console.log(this.order);
        const form = document.querySelector('form');
        const formHeader = document.querySelector('.form-header-wrapper');
        const heading = form.querySelector('h1');
        if (heading && !formHeader) {
            const wrapper = document.createElement('div');
            wrapper.classList.add('form-header-wrapper');
            heading.parentNode.insertBefore(wrapper, heading);
            wrapper.appendChild(heading);
        }
        this.teleportTo = '.form-header-wrapper';
        this.loaded = true;
    },
    methods: {
        cancelOrder() {
            let data = {
                order: this.order,
                message: 'Are you sure you want to Cancel this order?',
                method: 'cancel',
                confirmation: 'The order was Cancelled..',
                buttonText: 'Cancel Order',
            };
            this.$refs.warning.open(data);
        },
        startReturn() {
            this.$refs.createReturn.open(this.order, this.order.products);
        },
        refundOrder() {
            this.$refs.createRefund.open(this.order);
        },
        captureOrder() {
            let data = {
                order: this.order,
                message: 'Are you sure you want to capture the charge?',
                method: 'capture',
                confirmation: 'The order was captured..',
                buttonText: 'Capture',
            };
            this.$refs.warning.open(data);
        },
        markAsShipped() {
            let data = {
                order: this.order,
                message: 'Are you sure you want to mark this order as Shipped?',
                method: 'shipped',
                confirmation: 'The order was marked as Shipped..',
                buttonText: 'Mark as shipped',
            };
            this.$refs.warning.open(data);
        },
        markAsDelivered() {
            let data = {
                order: this.order,
                message:
                    'Are you sure you want to mark this order as Delivered?',
                method: 'delivered',
                confirmation: 'The order was marked as Delivered..',
                buttonText: 'Mark as delivered',
            };
            this.$refs.warning.open(data);
        },
        markAsReady() {
            let data = {
                order: this.order,
                message: 'Are you sure you want to mark this order as Ready?',
                method: 'shipped',
                confirmation: 'The order was marked as Ready..',
                buttonText: 'Mark as ready',
            };
            this.$refs.warning.open(data);
        },
        markAsPickedUp() {
            let data = {
                order: this.order,
                message:
                    'Are you sure you want to mark this order as Picked Up?',
                method: 'delivered',
                confirmation: 'The order was marked as Picked Up..',
                buttonText: 'Mark as picked up',
            };
            this.$refs.warning.open(data);
        },
    },
};
</script>
<style>
.form-header-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.form-header-wrapper h1 {
    margin-bottom: 0 !important;
}
</style>
