<template>
<div v-if="show" class="rf-wrp">
  <div  class="ed-wrapper">
      <div class="ed-inner-wrapper">
          <h3>{{this.message}}</h3>
          <div class="ed-actions">
              <default-button @click="save()" id="save">{{this.buttonText}}</default-button>
              <outline-button component="div" @click.prevent="close()">Cancel</outline-button>
          </div>
      </div>
  </div>
  </div>

</template>

<script>
import axios from 'axios'

export default {
    data(){
        return{
            show:false,
            message:'',
            confirmation:'',
            method:'',
            buttonText:'',
            order:{}
        }
    },
    methods:{

        open(data){
            this.show = true;
            this.order = data.order;
            this.message = data.message;
            this.method = data.method;
            this.buttonText = data.buttonText;
            this.confirmation = data.confirmation;
        },
        save(){
           axios.post(`/nova-custom-api/${this.method}`,{orderId: this.order.id}).then(resp=>{
               Nova.success(this.confirmation);
               window.location.reload();
            })
            .catch(err=>{
                Nova.error('Something went wrong')
            })

            this.show = false;
        },
        close(){
            this.show = false;
        }
    }
}
</script>

<style scoped>
textarea {
    height: 150px;
    padding: .75rem;
    margin-bottom: 15px;
}
h3 {
    padding: .75rem;
    margin-bottom: 15px;
    font-family: 'Lato', sans-serif;
    font-size: 18px;
    font-weight: 500;
}
select {
    padding: .75rem;
    margin-bottom: 15px;
}
.rf-wrp{
    background-color: rgba(124,133,142,.7);
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
}
.ed-wrapper{
    background-color: rgba(255, 255, 255.1);
    z-index: 10;
    /* height: 500px; */
    /* position: fixed; */
    display: flex;
    justify-content: center;
    align-items: center;
    top: 220px;
    background-color: white;
    border: lightgray solid 1px;
    border-radius: 5px;
    padding: 30px;
    width: 600px;
}
.ed-inner-wrapper{
    padding: 20px;
    overflow: scroll;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.ed-inner-wrapper input{
    margin-bottom: 20px;
    width: 100%;
}
.ed-actions{
    display: flex;
    gap: 1rem;
    align-self: flex-end;
}
</style>
