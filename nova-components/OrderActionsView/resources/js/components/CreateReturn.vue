<template>
    <div v-if="show" class="overlay">
        <div class="outer-wrapper" :class="{ loading: loading }">
            <div class="inner-wrapper">
                <div class="return-by-wrap">
                    <div class="return-by">Return By</div>
                    <select
                        class="return-by-select"
                        v-model="returnObj.dropoff"
                    >
                        <option disabled selected>Return by</option>
                        <option :value="true">In Store</option>
                        <option :value="false">By Mail</option>
                    </select>
                </div>

                <div
                    class="sing-prod-wrap"
                    v-for="(prod, index) in products"
                    :key="index"
                    v-show="prod.returns_left > 0"
                >
                    <div class="or-single-prod">
                        <input
                            type="checkbox"
                            class="chk"
                            v-model="selectedProducts"
                            :value="prod"
                        />
                        <div class="or-img">
                            <img :src="prod.media" :alt="prod.title" />
                        </div>
                        <div class="or-prod-info">
                            <p class="or-prod-title">{{ prod.title }}</p>
                            <p v-if="prod.sku" class="or-prod-sku">
                                SKU: {{ prod.sku
                                }}<span class="or-prod-stock"></span>
                            </p>
                        </div>
                        <p class="or-prod-price">
                            {{ currency(prod.price)
                            }}{{ ' x ' + prod.quantity }}
                        </p>
                        <p class="or-prod-price">
                            {{ currency(prod.price * prod.quantity) }}
                        </p>
                    </div>
                    <div
                        v-if="
                            selectedProducts.find(
                                (p) => p.id == prod.id && p.type == prod.type
                            )
                        "
                        class="sing-prod-select-wrap"
                    >
                        <select name="" id="" v-model="prod.quantity">
                            <option
                                v-for="(quan, index) in prod.returns_left"
                                :key="index"
                                :value="quan"
                            >
                                {{ quan }}
                            </option>
                        </select>
                        <select
                            name=""
                            id=""
                            v-model="prod.reason"
                            @change="selectReason(prod, $event)"
                        >
                            <option value="No longer needed">
                                No longer needed
                            </option>
                            <option value="Inaccurate website description">
                                Inaccurate website description
                            </option>
                            <option value="Item defective or doesn't work">
                                Item defective or doesn't work
                            </option>
                            <option value="Bought by mistake">
                                Bought by mistake
                            </option>
                            <option value="Better price available">
                                Better price available
                            </option>
                            <option value="Product damaged, but shpping box OK">
                                Product damaged, but shpping box OK
                            </option>
                            <option value="Item arrived too late">
                                Item arrived too late
                            </option>
                            <option value="Missing or broken parts">
                                Missing or broken parts
                            </option>
                            <option
                                value="Product and shipping box both damaged"
                            >
                                Product and shipping box both damaged
                            </option>
                            <option value="Wrong item was sent">
                                Wrong item was sent
                            </option>
                            <option
                                value="Received extra item I didn't buy (no refund needed)"
                            >
                                Received extra item I didn't buy (no refund
                                needed)
                            </option>
                            <option value="Too small/short">
                                Too small/short
                            </option>
                            <option value="Too large/long">
                                Too large/long
                            </option>
                            <option value="Fabric/material not as expected">
                                Fabric/material not as expected
                            </option>
                            <option value="Color/Pattern not as expected">
                                Color/Pattern not as expected
                            </option>
                        </select>
                    </div>
                </div>
                <breakdown ref="pricing" :breakdown="breakdown" />
            </div>

            <div class="footer">
                <outline-button component="div" @click="close()">
                    Cancel
                </outline-button>
                <default-button component="div" @click="create()">
                    Create Return
                </default-button>
            </div>
        </div>
    </div>
</template>

<script>
import Breakdown from './Breakdown';
import axios from 'axios';
import _ from 'lodash';

export default {
    components: {
        Breakdown,
    },

    data() {
        return {
            products: [],
            selectedProducts: [],
            breakdown: {},
            order: {},
            show: false,
            loading: false,
            returnObj: {
                order_id: Number,
                products: [],
                dropoff: true,
            },
        };
    },

    methods: {
        selectReason(prod, event) {
            let reason = event.target.value;
            prod.no_refund = false;

            const responsibilityMap = {
                'Inaccurate website description': true,
                "Item defective or doesn't work": true,
                'Product damaged, but shipping box OK': true,
                'Item arrived too late': true,
                'Missing or broken parts': true,
                'Product and shipping box both damaged': true,
                'Wrong item was sent': true,
                'Fabric/material not as expected': true,
                'Color/Pattern not as expected': true,
                'No longer needed': false,
                'Bought by mistake': false,
                'Better price available': false,
                'Too small/short': false,
                'Too large/long': false,
            };

            if (reason in responsibilityMap) {
                prod.our_responsibility = responsibilityMap[reason];
            }

            if (
                reason === "Received extra item I didn't buy (no refund needed)"
            ) {
                prod.no_refund = true;
            }
        },

        open(order, products) {
            this.order = order;
            this.products = products.map((p) => {
                return {
                    ...p,
                    reason: 'No longer needed',
                    our_responsibility: false,
                    no_refund: false,
                };
            });
            this.show = true;
        },
        close() {
            this.selectedProducts = [];
            this.show = false;
        },
        create() {
            if (!this.selectedProducts.length > 0) return;
            this.loading = true;
            this.returnObj.products = this.selectedProducts;
            this.returnObj.order_id = this.order.id;
            axios
                .post('/api/return/create', this.returnObj)
                .then((resp) => {
                    Nova.visit(`/resources/returns/${resp.data.id}`);
                })
                .catch((err) => {
                    this.loading = false;
                    Nova.error('An error occurred');
                });
        },
        getBreakdown() {
            axios
                .post('/api/returnBreakDown', {
                    order_id: this.order.id,
                    products: this.selectedProducts,
                })
                .then((resp) => {
                    this.breakdown = resp.data;
                })
                .catch((err) => {
                    if (err.response.status == 404) {
                        this.breakdown.sub_total = 0;
                        this.breakdown.tax_amount = 0;
                        this.breakdown.shipping_amount = 0;
                        this.breakdown.discount_amount = 0;
                        this.breakdown.grand_total = 0;
                    } else {
                        Nova.error('An error occurred');
                    }
                });
        },
        currency(value) {
            if (value) {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                }).format(value);
            }
        },
    },

    watch: {
        selectedProducts: {
            handler() {
                this.getBreakdown();
            },
            deep: true,
        },
    },
};
</script>

<style scoped>
.loading {
    opacity: 0.5;
}

.overlay {
    background-color: rgba(124, 133, 142, 0.7);
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: 100;
    display: flex;
    justify-content: center;
    overflow: auto;
}

.outer-wrapper {
    width: 50%;
    display: flex;
    flex-direction: column;
    margin: auto;
    margin-top: 80px;
}

.inner-wrapper {
    background-color: rgba(255, 255, 255, 1);
    border-top-left-radius: 13px;
    border-top-right-radius: 13px;
    padding: 30px;
    height: 600px;
    overflow: scroll;
}

.sing-prod-wrap {
    padding: 20px;
    border-bottom: #eff1f4 solid 1px;
}

.or-single-prod {
    display: flex;
    align-items: center;
}

.sing-prod-wrap:last-child {
    border-bottom: none;
}

.sing-prod-select-wrap {
    display: flex;
    margin-top: 15px;
}

.return-by-wrap {
    display: flex;
    align-items: center;
}

.return-by {
    width: 12%;
}

.sing-prod-select-wrap select,
.return-by-select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    font-size: 16px;
    font-weight: 300;
    width: 100%;
    height: 100%;
    background: #fff;
    border: #bacad6 solid 1px;
    border-radius: 5px;
    background-image: url(/img/grey_arrow_down.svg);
    background-repeat: no-repeat;
    background-position: 95%;
    padding-right: 17px;
    background-size: 11px;
    margin-right: 12px;
    outline: none;
    padding: 5px;
}

.return-by-select {
    width: 100px;
}

.sing-prod-select-wrap select:first-child {
    width: 30%;
}

.sing-prod-select-wrap select:last-child {
    margin-right: initial;
    background-position: 98%;
}

.or-img {
    height: 72px;
    width: 72px;
}

.or-img img {
    height: 100%;
    width: 100%;
    object-fit: contain;
}

.or-prod-info {
    margin-left: 15px;
    margin-right: 15px;
    display: flex;
    flex-direction: column;
    text-overflow: ellipsis;
    width: 50%;
}

.or-prod-title {
    color: #4099de;
    font-size: 17px;
    line-height: 22px;
}

.chk {
    margin-right: 15px;
}

.or-prod-sku {
    margin-top: 6px;
    color: #a8adb4;
    font-size: 14px;
    font-weight: 300;
}

.or-prod-price {
    margin-right: 40px;
    color: #525860;
    font-size: 18px;
    font-weight: 500;
    width: 25%;
}

.or-prod-price:last-child {
    margin-right: 10px;
    width: initial;
}

.footer {
    background-color: #f4f7fa;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding: 15px;
    align-items: center;
    border-bottom-left-radius: 13px;
    border-bottom-right-radius: 13px;
}

.btn {
    margin-left: 20px;
    cursor: pointer;
}

.btn-gray {
    background-color: #c3c3c3;
    color: white;
}

.btn-gray:hover {
    opacity: 0.7;
}

@media only screen and (max-width: 959px) {
    .outer-wrapper {
        width: 90%;
    }
}
</style>
