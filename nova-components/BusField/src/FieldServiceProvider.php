<?php

namespace Capitalc\BusField;

use Laravel\Nova\Nova;
use <PERSON>vel\Nova\Events\ServingNova;
use Illuminate\Support\ServiceProvider;

class FieldServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Nova::serving(function (ServingNova $event) {
            // Nova::script('bus-field', __DIR__.'/../dist/js/field.js');
            // Nova::style('bus-field', __DIR__.'/../dist/css/field.css');
        });
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
