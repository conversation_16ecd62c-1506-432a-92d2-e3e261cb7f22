<template>
</template>

<script>
import { <PERSON><PERSON><PERSON>, HandlesValidationErrors } from 'laravel-nova'
export default {
    mixins: [<PERSON><PERSON><PERSON>, HandlesValidationErrors],
    props: ['resourceName', 'resourceId', 'field'],
    data(){
        return {
            values: null, 
            product: {},
            variation: null, 
            meta: null, 
            creator: null,
            firstUpdate: true,
            digital: null,
            variation_images: null,
            release_date: null,
            notification: null,
        }
    },
    mounted() {
        this.meta = this.$parent.$children.find(item => {
                if(!item.field) return false;
                return item.field.attribute == 'meta'
            }
        )

        this.digital = this.$parent.$children.find(item => {
                if(!item.field
                // || !item.field.fields
                ) return false;
                return item.field
                // .fields[0]
                .attribute == 'digital'
            }
        )
        this.variation_images = this.$parent.$children.find(item => {
                if(!item.field) return false;
                return item.field.indexName == 'Variations Images'
            }
        )
        this.release_date = this.$parent.$children.find(item => {
                if(!item.field) return false;
                return item.field.indexName == 'Release Date'
            }
        )
        this.notification = this.$parent.$children.find(item => {
                if(!item.field) return false;
                return item.field.indexName == 'Follow Notification'
            }
        )

        this.release_date.$watch('value', (resource) => {
            let time = moment(resource);
            if (
                !resource
                || time.isBefore(moment().subtract(30, 'days'))
                || time.isAfter(moment())
            ) {
                this.notification.hidden = true
            }
            else {
                this.notification.hidden = false               
            }
        })

        this.item_type = this.$parent.$children.find(item => {
                if(!item.field) return false;
                return item.field.attribute == 'item_type'
            }
        )

        if (this.item_type.value != 'both' && this.item_type.value != 'digital') {
            this.digital.$el.classList.remove('flex')
            this.digital.$el.classList.add('hidden')
        }

        this.item_type.$watch('value', (value) => {
            if (value == 'both' || value == 'digital') {
                this.digital.$el.classList.remove('hidden')
                this.digital.$el.classList.add('flex')
            } else {
                this.digital.$el.classList.remove('flex')
                this.digital.$el.classList.add('hidden')
            }
        })

        this.variation = this.$parent.$children.find(item => {
                if(!item.field) return false;
                return item.field.attribute == 'variations'
            }
        )

        var type = this.$parent.$children.find(item => {
                if(!item.field) return false;
                return item.field.attribute == 'product_type'
            }
        )

        this.creator = this.$parent.$children.find(item => {
                if(!item.field) return false;
                return item.field.attribute == 'creators_ids'
            }
        )
        if (!type.selectedResourceId) { this.firstUpdate = false }
        // this.creator.$el.className = 'hidden'
        type.$watch('selectedResource', (resource) => {
            this.update(type, this.meta, this.variation, _.get(resource, 'value'))
        })
    },

    methods: {
        update(type, meta, variation, resource) {
            //remove empty meta
            this.$set(meta, 'theData', _.filter(meta.theData, (item) => {
                return item.key && item.value
            }))

            //add new meta and varations
            if (resource) {
                axios(`/nova-api/product-types/${resource}`)
                    .then(success => {
                        if (this.firstUpdate) {
                            this.firstUpdate = false
                        } else {
                            this.updateVars(success)
                        }
                        this.updateFields(success)
                        this.updateCreators(success)
                    })
            } else {
                this.$set(this.creator.field, 'extraAttributes.hide', true);
            }
        },

        updateCreatorsOLD (type, creator, resource) {
            let type_string = type.field.types.find(item => item.id == resource)
            if (
                _.get(type_string, 'creator_type')
                && creator.field.types[type_string['creator_type']]
                ) {
                let types = creator.field.types[type_string['creator_type']]
                let array = [];
                types.forEach(item => {
                    array.push({display: item.name, value: item.id})
                })
                creator.availableResources = array
            } else{
                creator.availableResources = []
            }
        },
        guid() {
            var S4 = function() {
                return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
            }
            return S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4()
        },

        updateVars(success) {
            let add_vars = success.data.resource.fields.find(item => {
                    return item.attribute == 'variations'
                }).value
                if (add_vars) {
                    add_vars.map(item => {
                        let exists = this.variation.variations.find(variation => {
                            return item.name == variation.name
                        })
                        if (!exists) {
                            this.variation.variations.push({
                                name: item.name,
                                info: item.info,
                                values: [],
                                filter: false,
                                assistance: false,
                            })
                        }
                    })
                }
        },
        updateFields(success) {
            let add_fields = success.data.resource.fields.find(item => {
                return item.attribute == 'fields'
            }).value

            if (add_fields) {
                _.forEach(add_fields, (item, index) => {
                    let exists = this.meta.theData.find(meta => {
                        return index == meta.key
                    })
                    if (!exists) {
                        this.$set(this.meta, 'theData', [...this.meta.theData, { id: this.guid(), key: index, value: item }])
                    }
                })
            }
        },
            
        updateCreators(old) {
            let creator_type = old.data.resource.fields.find(item => {
                return item.attribute == 'creator_type'
            }).value
            
            axios(`/api/product_type/${old.data.resource.id.value}/creators?product_id=` + _.get(this, 'resourceId', ''))
                .then(success => {
                    if (_.get(success.data, 'hide')) {
                        this.creator.field.extraAttributes.hide = true
                        return;
                    }
                    this.creator.field.extraAttributes.hide = false
                    this.creator.field.extraAttributes.label = _.capitalize(creator_type)
                    this.creator.field.extraAttributes.type = creator_type
                    this.creator.availableResources = success.data
                    this.creator.value = this.creator.value.filter(value => {
                        return _.findIndex(success.data, (item) => {
                            return item.value == value
                        }) >= 0
                    })
                }).catch( err => {
                    this.creator.field.extraAttributes.hide = true
                })
        },
        fill(formData) {},
    },
}
</script>
