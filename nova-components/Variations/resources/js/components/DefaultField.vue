<template>
    <div class="space-y-2 md:flex md:flex-row md:space-y-0 py-3">
        <div class="w-full shrink-0 px-6 md:mt-2 @md/modal:mt-2 md:px-8 @md/modal:px-8 md:w-1/5 @md/modal:w-1/5">
            <form-label class="space-x-1">{{ label }}</form-label>
        </div>
        <div class="w-full space-y-2 px-6 md:px-8 @md/modal:px-8" :class="width">
            <slot></slot>
        </div>
    </div>
</template>
<script>
export default {
    name: "DefaultField",
    props: {
        label: {
            type: String,
        },
        width: {
            type: String,
            default: 'md:w-3/5 @md/modal:w-3/5'
        }
    },
}
</script>
<style scoped>

</style>
