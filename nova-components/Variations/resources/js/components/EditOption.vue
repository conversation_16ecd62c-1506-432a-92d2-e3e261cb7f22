<template>
    <div class="mb-3 border border-gray-300 rounded">
        <div class="flex items-center justify-between bg-gray-100 rounded p-3">
            <icon-button
                iconType="chevron-down"
                @click="toggleVisibility"
                solid
            />
            <h2>{{ option.name || 'New option set' }}</h2>
            <p>
                {{
                    option.values
                        ? option.values.map((value) => value.name).join(', ')
                        : ''
                }}
            </p>
            <icon-button
                iconType="trash"
                solid
                small
                @click.prevent="removeOption"
            />
        </div>
        <transition
            enter-from-class="opacity-0"
            leave-to-class="opacity-0"
            enter-active-class="transition duration-300"
            leave-active-class="transition duration-300"
        >
            <div v-if="option.isVisitable">
                <default-field label="Option Name">
                    <input
                        v-model.lazy="option.name"
                        class="w-full form-control form-input form-control-bordered"
                        placeholder="Option Name"
                    />
                </default-field>
                <option-values-field v-model="option.values" />
                <form-checkbox
                    :field="{
                        indexName: 'Filter',
                        name: 'Show in Filters and Assistance',
                        attribute: `${option.name}-filter`,
                        value: option.filter,
                        component: 'checkbox',
                    }"
                    @changed="updateOption('filter', $event)"
                />
                <field-wrapper>
                    <div class="w-1/5 py-6 px-8">
                        <form-label label-for="info">Explanation</form-label>
                    </div>
                    <div class="py-6 px-8 w-full">
                        <froala
                            :tag="'textarea'"
                            :config="config"
                            v-model="option.explanation"
                        ></froala>
                    </div>
                </field-wrapper>
            </div>
        </transition>
    </div>
</template>

<script>
import DefaultField from './DefaultField'
import OptionValuesField from './OptionValuesField'
export default {
    components: { DefaultField, OptionValuesField },
    props: {
        option: Object,
        index: Number,
        froalaKey: String
    },
    data() {
        return {
            config: {
                key: '',
                quickInsertEnabled: false,
                heightMin: 200,
                linkStyles: {
                    'com_btn com_btn_green': 'Green Button',
                    'com_btn com_btn_gold': 'Gold Button',
                },
                linkMultipleStyles: false,
                toolbarButtons: [
                    ['paragraphFormat', 'bold', 'italic', 'underline'],
                    ['formatOL', 'formatUL'],
                    ['insertLink'],
                    ['html'],
                ],
            }
        }
    },
    beforeMount() {
        this.config.key = this.froalaKey;
    },
    methods: {
        toggleVisibility() {
            this.$emit('toggle-visibility', this.index, !this.option.isVisitable);
        },
        removeOption() {
            this.$emit('remove', this.index);
        },
        updateOption(key, value) {
            this.$emit('update', this.index, key, value);
        }
    }
}
</script>
