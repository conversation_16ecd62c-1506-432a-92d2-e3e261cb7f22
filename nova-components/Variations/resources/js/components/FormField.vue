<template>
    <div>
        <div class="px-6 py-5">
            <div
                v-if="options.length"
                class="w-full rounded border border-gray-100"
            >
                <edit-option
                    v-for="(option, optionIndex) in options"
                    :key="optionIndex"
                    :option="option"
                    :froala-key="field.froala_key"
                    :index="optionIndex"
                    @toggle-visibility="setOptionVisibility"
                    @remove="removeOption"
                    @update="updateOption"
                />
            </div>
            <div
                class="flex items-center w-full"
                :class="[options.length ? 'justify-end' : 'justify-between']"
            >
                <form-label v-if="!options.length"
                    >Add options if this product comes in multiple versions,
                    like different sizes or colors.
                </form-label>
                <default-button @click.prevent="addOption">Add Option</default-button>
            </div>
        </div>
        <div class="px-6 py-5 form-field">
            <label>Default Variation</label>
            <div class="form-control-wrap">
                <select v-model="defaultVariation" class="w-full rounded border border-gray-100">
                    <option value="">Select a variation</option>
                    <option v-for="variation in variations" :key="variation.id" :value="JSON.stringify(variation.values)">
                        {{ variation.store_title }}
                    </option>
                </select>
            </div>
        </div>
        <variations :options="options" :variations="variations" :media="media"
                    @removeImage="removeImage"
                    @uploadImage="uploadImage" @update="updateVariations" />
    </div>
</template>

<script>
import {DependentFormField, HandlesValidationErrors} from 'laravel-nova'
import Variations from "./Variations.vue";
import EditOption from "./EditOption.vue";
import _ from 'lodash';

const EMPTY_OPTION = {
    name: null,
    values: [],
    filter: false,
    assistance: false,
    explanation: null,
    isVisitable: true,
};

export default {
    mixins: [DependentFormField, HandlesValidationErrors],
    components: {Variations, EditOption},

    props: ['resourceName', 'resourceId', 'field', 'panel'],

    data() {
        return {
            options: [],
            variations: [],
            loading: false,
            media: [],
            defaultValues: {
                price: 0,
                sku: null,
                cost_price: null,
                list_price: null,
                store_price: null,
                add_online_price: '',
                online_price: null,
                online_price_based_on: null,
                online_price_percent: null,
                sale_price: null,
                add_sale: false,
                sale_type: '',
                sale_from: '',
                sale_amount: null,
                start_sale: null,
                end_sale: null,
                barcode: null,
                gtni: null,
                track_inventory: true,
                store_quantity: null,
                website_quantity: null,
                max_quantity: null,
                item_type: '',
                width: null,
                height: null,
                length: null,
                weight: null,
                boxes: null,
            },
            defaultVariation: ''
        }
    },

    watch: {
        options: {
            handler: function (options) {
                if(this.loading) return;
                this.generateVariations(options);
            },
            deep: true
        }
    },
    methods: {
        setInitialValue() {
            this.loading = true;
            const variations = this.field.value || [];
            for (const index in variations) {
                variations[index]['values'] = variations[index]['values'].map(value => {
                    return {name: value};
                });
            }
            this.options = variations;
            const variation_info = this.field.variation_info || [];
            for (const index in variation_info) {
                variation_info[index]['values'] = JSON.parse(variation_info[index]['meta']);
                delete variation_info[index]['meta'];
                if (!variation_info[index]['store_title']) {
                    variation_info[index]['store_title'] = Object.values(variation_info[index]['values']).join(' - ');
                }
            }
            this.variations = variation_info;
            this.media = this.field.media || [];
            if (this.field.default_variation) {
                this.defaultVariation = this.field.default_variation;
            } else if(this.variations && this.variations.length) {
                this.defaultVariation = JSON.stringify(this.variations[0].values);
            } else {
                this.defaultVariation = '';
            }
            this.loading = false;
        },
        fill(formData) {
            const variations = JSON.parse(JSON.stringify(this.options));
            for (const index in variations) {
                variations[index]['values'] = variations[index]['values'].map(value => value.name);
            }
            formData.append('variations', JSON.stringify(variations));
            const variation_info = JSON.parse(JSON.stringify(this.variations));
            for (const index in variation_info) {
                variation_info[index]['meta'] = variation_info[index]['values'];
                delete variation_info[index]['values'];
            }
            formData.append('variation_info', JSON.stringify(variation_info));
            this.options.forEach((option => {
                option.values.forEach(value => {
                    if (value.file) {
                        formData.append(`option_${option.name}_value_${value.name}`, value.file);
                    }
                });
            }));
            this.media.forEach((mediaItem, index) => {
                formData.append(`variation_media[${index}][uuid]`, mediaItem.uuid);
                // Include the _id for both new and existing media
                if (mediaItem._id) {
                    formData.append(`variation_media[${index}][_id]`, mediaItem._id);
                }
                if (mediaItem.file) {
                    formData.append(`variation_media[${index}][file]`, mediaItem.file);
                } else {
                    formData.append(`variation_media[${index}][original_url]`, mediaItem.original_url);
                }
            });
            formData.append('default_variation', this.defaultVariation);
        },
        addOption() {
            this.options = [...this.options, {...EMPTY_OPTION}];
        },
        removeOption(index) {
            this.options = this.options.filter((_, i) => i !== index);
        },
        updateOption(index, key, value) {
            this.options = this.options.map((option, i) => {
                if (i === index) {
                    return {...option, [key]: value};
                }
                return option;
            });
        },
        setOptionVisibility(index, isVisible) {
            this.updateOption(index, 'isVisitable', isVisible);
        },
        generateValues(options) {
            function combine(index, current) {
                if (index === options.length) {
                    variations.push(Object.assign({}, ...current));
                    return;
                }

                for (const value of options[index].values) {
                    combine(index + 1, [...current, { [options[index].name]: value.name }]);
                }
            }

            const variations = [];
            combine(0, []);
            return variations;
        },
        generateVariations(options) {
            const variations = [];
            const variationValues = this.generateValues(options);
            variationValues.forEach(variationValue => {
                const title = Object.values(variationValue).join(' - ');
                const alreadyExists = this.variations.find(v => {
                    return _.isEqual(v.values, variationValue);
                });
                if (alreadyExists) {
                    variations.push(alreadyExists);
                    return;
                }
                variations.push({
                    ...this.defaultValues,
                    id: 'new-' + title,
                    values: variationValue,
                    store_title: title,
                    visibility: true
                });
            });
            this.variations = variations;
        },
        updateVariations(variations) {
            this.variations = variations;
        },
        onSyncedField() {
            const dependentFieldValues = this.dependentFieldValues;
            this.defaultValues = Object.keys(this.defaultValues).reduce((acc, key) => {
                if (dependentFieldValues[key]) {
                    acc[key] = dependentFieldValues[key];
                } else {
                    acc[key] = this.defaultValues[key];
                }
                return acc;
            }, {});
        },
        uploadImage(file) {
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    // Generate a unique _id for immediate frontend use
                    const _id = Math.random().toString(36).substr(2, 9);
                    this.media = [
                        ...this.media,
                        {
                            _id: _id, // Frontend-generated _id for immediate use
                            preview: e.target.result,
                            original_url: e.target.result, // Add for consistency
                            file: file,
                        },
                    ]
                };
                reader.readAsDataURL(file);
            }
        },
        removeImage(_id) {
            this.media = this.media.filter(media => media._id !== _id);
        },
    },

}
</script>
<style lang="scss" scoped>
.form-field {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-top: 1px solid rgb(var(--colors-slate-200));

    &:not(:last-child) {
        border-bottom: 1px solid rgb(var(--colors-slate-200));
    }

    label {
        color: rgb(var(--colors-slate-600));
        font-weight: bold;
        font-size: 0.875rem;
        width: 25%;
        flex-shrink: 0;
    }

    .form-control-wrap {
        display: flex;
        flex-grow: 1;

        span {
            display: flex;
            align-items: center;
            padding: 0.5rem 0.8rem;
            background: rgb(var(--colors-slate-100));
            border: 1px solid rgb(var(--colors-slate-200));
            color: rgb(var(--colors-slate-600));
            font-size: 1rem;

            &:first-child {
                border-top-left-radius: 0.5rem;
                border-bottom-left-radius: 0.5rem;
            }

            &:last-child {
                border-top-right-radius: 0.5rem;
                border-bottom-right-radius: 0.5rem;
            }
        }

        input:not([type="checkbox"]), select {
            flex-grow: 1;
            border: 1px solid rgb(var(--colors-slate-300));
            font-size: 1rem;
            padding: 0.5rem;

            &:first-child {
                border-top-left-radius: 0.5rem;
                border-bottom-left-radius: 0.5rem;
            }

            &:last-child {
                border-top-right-radius: 0.5rem;
                border-bottom-right-radius: 0.5rem;
            }

            &:focus {
                border: 1px solid rgb(var(--colors-primary-500));
            }
        }
    }
}
</style>
