<template>
    <default-field label="Option Values" width="w-full">
        <div class="w-full">
            <draggable v-model="values" tag="div" item-key="name">
                <template #item="{element, index}">
                    <div class="flex items-center mb-3 gap-4">
                        <IconButton
                            iconType="selector"
                            solid
                            small
                        />
                        <div class="w-10 h-10 shrink-0">
                            <div v-if="element.image" class="relative">
                                <img :src="element.image" class="w-10 h-10 rounded object-cover" />
                                <div type="button" class="remove-icon" @click.prevent="removeImage(index)">
                                    <Icon type="x" />
                                </div>
                            </div>
                            <div v-else class="relative">
                                <Icon type="photograph" class="hover:opacity-50 w-10 h-10 cursor-pointer" @click.prevent="$refs[`file${index}`].click()" />
                                <input :ref="`file${index}`" type="file" class="hidden" @change="setImage($event, index)" />
                            </div>
                        </div>
                        <input v-model.lazy="values[index].name" class="w-full form-control form-input form-control-bordered" placeholder="Value Name" />
                        <div class="flex items-center gap-1">
                            <IconButton
                                iconType="trash"
                                solid
                                small
                                @click.prevent="removeValue(index)"
                            />
                        </div>
                    </div>
                </template>
            </draggable>
            <div class="flex items-center" :class="[values.length ? 'justify-end' : 'justify-between']">
                <p class="text-xs text-gray-500" v-if="!values || !values.length">Add values for this option</p>
                <default-button @click.prevent="addValue">Add Value</default-button>
            </div>
        </div>
    </default-field>
</template>

<script>

import {defineComponent} from "vue";
import draggable from "vuedraggable";
import DefaultField from "./DefaultField.vue";

const EMPTY_VALUE = {
    name: null,
    image: null,
    file: null
};

export default defineComponent({
    components: {draggable, DefaultField},
    props: {
        modelValue: {
            type: Array,
            default: () => []
        },
    },
    emits: ['update:modelValue'],
    computed: {
        values: {
            get() {
                return this.modelValue;
            },
            set(value) {
                this.$emit('update:modelValue', value);
            }
        }
    },
    methods: {
        addValue() {
            this.values = [...this.values, { ...EMPTY_VALUE }];
        },
        removeValue(index) {
            this.values = this.values.filter((_, i) => i !== index);
        },
        setImage(event, index) {
            const file = event.target.files[0];
            const reader = new FileReader();
            reader.onload = (e) => {
                this.values[index].image = e.target.result;
                this.values[index].file = file;
            }
            reader.readAsDataURL(file);
        },
        removeImage(index) {
            this.values[index].image = null;
            this.values[index].file = null;
        }
    }
})
</script>
