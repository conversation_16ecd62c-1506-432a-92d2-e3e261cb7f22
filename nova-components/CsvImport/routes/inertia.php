<?php

use Illuminate\Support\Facades\Route;
use Laravel\Nova\Http\Requests\NovaRequest;

/*
|--------------------------------------------------------------------------
| Tool Routes
|--------------------------------------------------------------------------
|
| Here is where you may register Inertia routes for your tool. These are
| loaded by the ServiceProvider of the tool. The routes are protected
| by your tool's "Authorize" middleware by default. Now - go build!
|
*/

Route::get('/', function (NovaRequest $request) {
    return inertia('csv-import');
});
Route::get('/preview/{file}', function (NovaRequest $request, $file) {
    return inertia('csv-import-preview', [
        'file' => $file,
    ]);
});

Route::get('/review/{file}', function (NovaRequest $request, $file) {
    return inertia('csv-import-review', [
        'file' => $file,
    ]);
});
